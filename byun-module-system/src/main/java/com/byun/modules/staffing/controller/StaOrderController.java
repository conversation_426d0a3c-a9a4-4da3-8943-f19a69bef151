package com.byun.modules.staffing.controller;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.byun.common.util.DateUtils;
import com.byun.modules.staffing.vo.*;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.service.*;
import com.byun.modules.system.vo.SysUserInfoVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.ModelAndView;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.util.DateUtil;
import com.byun.common.util.RedisUtil;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.model.StaWorkModel;
import com.byun.modules.staffing.service.*;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import com.byun.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.util.WxlConvertUtils;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

/**
 * @Description: 任务单
 * @Author: baiyun
 * @Date: 2021-11-14
 * @Version: V1.0
 */
@Api(tags = "任务单")
@RestController
@RequestMapping("/staffing/staOrder")
@Slf4j
///staffing/staOrderresign
public class StaOrderController {
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private IStaEnrollInfoService staEnrollInfoService;
    @Autowired
    private IStaWorkService staWorkService;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IStaWorkAgentRelService staWorkAgentRelService;
    @Autowired
    private IStaScheduleService staScheduleService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IStaBlacklistService staBlacklistService;
    @Autowired
    private ISysUserRelService sysUserRelService;
    @Autowired
    private IStaTaskDateService staTaskDateService;
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;
    @Autowired
    private IStaTaskDeductionService staTaskDeductionService;
    @Autowired
    private IStaUserSettlementService staUserSettlementService;

    /**
     * 获取单日签到信息
     */
    @AutoLog(value = "签到信息-分页列表查询")
    @ApiOperation(value = "签到信息-分页列表查询", notes = "签到信息-分页列表查询")
    @GetMapping(value = "/lcList")
    public Result<?> queryPageLocationClockList(StaLocationClock staLocationClock, HttpServletRequest req) {
        if (WxlConvertUtils.isEmpty(staLocationClock.getStaOrderId())) {
            return Result.error("未找到订单id");
        }
        if (WxlConvertUtils.isEmpty(staLocationClock.getTime())) {
            return Result.error("未找到当前查询时间");
        }
        String formatStringDate = DateUtil.getFormatStringDate(staLocationClock.getTime());
        LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        locationClockLambdaQueryWrapper.likeRight(StaLocationClock::getTime, formatStringDate);
        locationClockLambdaQueryWrapper.eq(StaLocationClock::getStaOrderId, staLocationClock.getStaOrderId());
        locationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        locationClockLambdaQueryWrapper.orderByAsc(StaLocationClock::getTime);
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(locationClockLambdaQueryWrapper);
        return Result.OK(staLocationClocks);
    }
    /**
     * column  order
     *
     * @param pageNo  页
     * @param pageSize 条数
     * @param realName    姓名
     * @param userIdCard  身份证号
     * @param stateFlag   打卡状态
     * @param deptName   部门名字
     * @param order           排序
     * @param request  wid 任务id
     * @return  考勤记录
     */
    @GetMapping("/viewclockIn")
    public Result<?> viewclockIn(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                 @RequestParam(name = "realName", required = false) String realName,
                                 @RequestParam(name = "userIdCard", required = false) String userIdCard,
                                 @RequestParam(name = "dateRange_begin", required = false) String dateRangeBegin,
                                 @RequestParam(name = "dateRange_end", required = false) String dateRangeEnd,
                                 @RequestParam(name = "stateFlag", required = false) String stateFlag,
                                 @RequestParam(name = "deptName", required = false) String deptName,
                                 @RequestParam(name = "deptNo", required = false) String deptNo,
                                 @RequestParam(name = "order", required = true) String order,
                                 @RequestParam(name = "businessDivisionName", required = false) String businessDivisionName,
                                 @RequestParam(name = "regionName", required = false) String regionName, HttpServletRequest request){
        //TODO 日期没有使用 参数需要重新对比一下，删除无效参数，添加有效参数
        // 开始总耗时统计
        long startTime = System.currentTimeMillis();
        log.info("=== /viewclockIn 接口开始执行 ===");
        log.info("请求参数：pageNo={}, pageSize={}, realName={}, userIdCard={}, dateRangeBegin={}, dateRangeEnd={}, stateFlag={}, deptName={}, deptNo={}, order={}, businessDivisionName={}, regionName={}",
                pageNo, pageSize, realName, userIdCard, dateRangeBegin, dateRangeEnd, stateFlag, deptName, deptNo, order, businessDivisionName, regionName);

        try {
            // 1. 参数解析耗时统计
            long paramStartTime = System.currentTimeMillis();
            String wid = request.getParameter("wid");
            long paramEndTime = System.currentTimeMillis();
            log.info("参数解析耗时：{}ms, wid={}", (paramEndTime - paramStartTime), wid);

            // 2. 用户认证耗时统计
            long authStartTime = System.currentTimeMillis();
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (WxlConvertUtils.isEmpty(sysUser)) {
                log.warn("用户认证失败，登录失效");
                return Result.error("登录失效");
            }
            long authEndTime = System.currentTimeMillis();
            log.info("用户认证耗时：{}ms, 用户：{}", (authEndTime - authStartTime), sysUser.getUsername());

            // 3. 业务查询耗时统计
            long queryStartTime = System.currentTimeMillis();
            Page<StaLocationClock> clockInCollection = staOrderService.getPunchRecord(
                    pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                    dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, order);
            long queryEndTime = System.currentTimeMillis();
            log.info("业务查询耗时：{}ms, 查询结果：总记录数={}, 当前页记录数={}",
                    (queryEndTime - queryStartTime),
                    clockInCollection.getTotal(),
                    clockInCollection.getRecords().size());

            // 4. 响应构建耗时统计
            long responseStartTime = System.currentTimeMillis();
            Result<?> result = Result.OK(clockInCollection);
            long responseEndTime = System.currentTimeMillis();
            log.info("响应构建耗时：{}ms", (responseEndTime - responseStartTime));

            // 总耗时统计
            long totalTime = System.currentTimeMillis() - startTime;

            // 计算各阶段耗时
            long paramTime = paramEndTime - paramStartTime;
            long authTime = authEndTime - authStartTime;
            long queryTime = queryEndTime - queryStartTime;
            long responseTime = responseEndTime - responseStartTime;

            log.info("=== /viewclockIn 接口执行完成 ===");
            log.info("接口总耗时：{}ms", totalTime);

            // Controller层详细性能统计报告
            log.info("=== Controller层性能统计报告 ===");
            log.info("📊 各阶段耗时统计：");
            log.info("┌─────────────────────────────────────────────────────────────┐");
            log.info("│ 阶段名称                    │ 耗时(ms) │ 占比(%) │ 说明       │");
            log.info("├─────────────────────────────────────────────────────────────┤");
            log.info("│ 1. 参数解析                 │ {}ms │ {}% │ 请求参数   │", String.format("%8d", paramTime), String.format("%.1f", paramTime * 100.0 / totalTime));
            log.info("│ 2. 用户认证                 │ {}ms │ {}% │ 身份验证   │", String.format("%8d", authTime), String.format("%.1f", authTime * 100.0 / totalTime));
            log.info("│ 3. 业务查询                 │ {}ms │ {}% │ Service层  │", String.format("%8d", queryTime), String.format("%.1f", queryTime * 100.0 / totalTime));
            log.info("│ 4. 响应构建                 │ {}ms │ {}% │ 结果封装   │", String.format("%8d", responseTime), String.format("%.1f", responseTime * 100.0 / totalTime));
            log.info("├─────────────────────────────────────────────────────────────┤");
            log.info("│ 总计                        │ {}ms │ {}% │ -          │", String.format("%8d", totalTime), "100.0");
            log.info("└─────────────────────────────────────────────────────────────┘");

            // 查询结果统计
            log.info("📈 查询结果统计：");
            log.info("• 总记录数：{}条", clockInCollection.getTotal());
            log.info("• 当前页记录数：{}条", clockInCollection.getRecords().size());
            log.info("• 查询效率：{}条/秒", String.format("%.2f", queryTime > 0 ? (clockInCollection.getTotal() * 1000.0 / queryTime) : 0));

            // 性能分析
            log.info("🔍 Controller层性能分析：");
            if (queryTime > totalTime * 0.8) {
                log.info("• 主要耗时在业务查询（{}%），Service层是性能瓶颈", String.format("%.1f", queryTime * 100.0 / totalTime));
            } else if (authTime > totalTime * 0.3) {
                log.info("• 用户认证耗时较长（{}%），可能需要优化认证机制", String.format("%.1f", authTime * 100.0 / totalTime));
            } else {
                log.info("• Controller层各阶段耗时均衡，性能良好");
            }

            // 总体性能评级
            if (totalTime > 1000) {
                log.warn("⚠️ 接口响应时间较慢：{}ms，建议优化", totalTime);
                log.warn("🔧 优化重点：业务查询耗时{}ms（占{}%）", queryTime, String.format("%.1f", queryTime * 100.0 / totalTime));
            } else if (totalTime > 500) {
                log.info("⚡ 接口响应时间：{}ms，性能良好", totalTime);
            } else {
                log.info("🚀 接口响应时间：{}ms，性能优秀", totalTime);
            }

            log.info("=== Controller层统计报告结束 ===");

            return result;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("❌ /viewclockIn 接口执行异常，总耗时：{}ms", totalTime, e);
            return Result.error("查询打卡记录失败：" + e.getMessage());
        }
    }

    /**
     * 网页导出考勤信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/exportXlsAttendanceExport")
    public ModelAndView exportXlsAttendanceExport(HttpServletRequest request, StaSchedule sc, @RequestParam(name = "realName", required = false) String realName, @RequestParam(name = "userIdCard", required = false) String userIdCard, @RequestParam(name = "fieldTime", required = false) String fieldTime, @RequestParam(name = "startDate", required = false) String startDate, @RequestParam(name = "entDate", required = false) String entDate) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaSchedule> queryWrapper = QueryGenerator.initQueryWrapper(sc, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
            return mv;
        }
        //只查当前登录机构下的订单
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }

            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, sysUser.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                return mv;
            }
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            return mv;
        }
        String wid = request.getParameter("wid");
        List<StaOrder> staOrders = new ArrayList<>();
        //当前任务下所有签到记录
        List<StaLocationClock> staLocationClocks = new ArrayList<>();
        if (wid != null && wid.length() > 0) {
            staOrders = staOrderService.list(new QueryWrapper<StaOrder>().eq("sta_work_id", wid));
            staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().eq("sta_work_id", wid).eq("del_flag", CommonConstant.DEL_FLAG_0));
        }

        //订单集合
        List<String> oids = new ArrayList<>();
        String staWorkId = staOrders.get(0).getStaWorkId();
        //排班集合
        List<StaSchedule> staSchedules = new ArrayList<>();
        queryWrapper = new QueryWrapper<StaSchedule>();
        queryWrapper.eq("sta_work_id", staWorkId);
        if (StringUtils.isNotEmpty(userIdCard)) {
            queryWrapper.eq("user_id_card", userIdCard);
        }
        if (StringUtils.isNotEmpty(realName)) {
            queryWrapper.like("real_name", realName);
        }
        if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(entDate)) {
            queryWrapper.between("schedule_day", startDate.substring(0, 11), entDate.substring(0, 11));
        }
        staSchedules = staScheduleService.list(queryWrapper);
        /**
         * 只需要排班和签到进行关联 staSchedules -- staLocationClocks
         */
        List<lcDayPage> lcDayPages = new ArrayList<>();
        List<lcDayPage> ldp = new ArrayList<>(); //模板
        //循环排班 填充模板
        for (StaSchedule staSchedule : staSchedules) {
            if (staSchedule.getStartTime() != null) {
                lcDayPage lcDayPage = new lcDayPage();
                lcDayPage.setScheduleId(staSchedule.getId());
                lcDayPage.setTimeType(CommonConstant.CLOCK_TYPE_1);//签到
                lcDayPage.setTimeExpect(staSchedule.getStartTime());
                lcDayPage.setLcList(new StaLocationClock());
                lcDayPage.setSysUserId(staSchedule.getSysUserId());
                lcDayPage.setUserIdCard(staSchedule.getUserIdCard());
                lcDayPage.setRealName(staSchedule.getRealName());
                lcDayPage.setScheduleDay(staSchedule.getScheduleDay());
                lcDayPage.setStaOrderId(staSchedule.getStaOrderId());
                lcDayPage.setShiftCode(staSchedule.getCode());
                ldp.add(lcDayPage);
            }
            if (staSchedule.getEndTime() != null) {
                lcDayPage lcDayPage = new lcDayPage();
                lcDayPage.setScheduleId(staSchedule.getId());
                lcDayPage.setTimeType(CommonConstant.CLOCK_TYPE_2);//签退
                lcDayPage.setTimeExpect(staSchedule.getEndTime());
                lcDayPage.setLcList(new StaLocationClock());
                lcDayPage.setSysUserId(staSchedule.getSysUserId());
                lcDayPage.setUserIdCard(staSchedule.getUserIdCard());
                lcDayPage.setRealName(staSchedule.getRealName());
                lcDayPage.setScheduleDay(staSchedule.getScheduleDay());
                lcDayPage.setStaOrderId(staSchedule.getStaOrderId());
                lcDayPage.setShiftCode(staSchedule.getCode());
                ldp.add(lcDayPage);
            }
        }
        for (com.byun.modules.staffing.vo.lcDayPage dayPage : ldp) {
            for (int i = 0; i < staLocationClocks.size(); i++) {
                /**
                 * 条件1：排班和签到是否是同一天
                 * 条件2：订单id是否相同
                 * 条件3：上签退类型是否相等
                 */
                if (isSameDay(dayPage.getScheduleDay(), staLocationClocks.get(i).getTime()) && dayPage.getStaOrderId().equals(staLocationClocks.get(i).getStaOrderId()) && dayPage.getTimeType().equals(staLocationClocks.get(i).getTimeType())) {
                    //staLocationClocks不为空 长度大于1
                    if (!staLocationClocks.isEmpty() && staLocationClocks.size() > i) {
                        StaLocationClock staLocationClock = staLocationClocks.get(i);
                        staLocationClock.setScheduleDay(dayPage.getScheduleDay());
                        staLocationClock.setShiftCode(dayPage.getShiftCode());
                        dayPage.setLcList(staLocationClock);
                    }
                }
            }
            lcDayPages.add(dayPage);
        }
        ldp = new ArrayList<>();
        for (int i = 0; i < lcDayPages.size(); i++) {
            if (lcDayPages.get(i).getLcList().getId() == null) {
                if (lcDayPages.get(i).getTimeType() == 0 && (lcDayPages.get(i).getLcList().getId() == "" || lcDayPages.get(i).getLcList().getId() == null)) {
                    lcDayPages.remove(i);
                    continue;
                }
                //缺卡
                StaLocationClock staLocationClock = new StaLocationClock();
                staLocationClock.setTimeExpect(lcDayPages.get(i).getTimeExpect());//要求签到时间
                staLocationClock.setStaOrderId(lcDayPages.get(i).getStaOrderId());//订单id
                staLocationClock.setTimeType(lcDayPages.get(i).getTimeType());//缺卡的是签到或签退
                staLocationClock.setScheduleDay(lcDayPages.get(i).getScheduleDay());
                staLocationClock.setStaScheduleId(lcDayPages.get(i).getScheduleId());
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_0);
                staLocationClock.setShiftCode(lcDayPages.get(i).getShiftCode());
                lcDayPages.get(i).setLcList(staLocationClock);
            }
        }
        List<StaLocationClock> slc = new ArrayList<>();
        for (lcDayPage lcd : lcDayPages) {
            StaLocationClock lcList = lcd.getLcList();
            lcList.setRealName(lcd.getRealName());
            lcList.setUserIdCard(lcd.getUserIdCard());
            lcList.setSysUserId(lcd.getSysUserId());
            lcd.setLcList(lcList);
            slc.add(lcd.getLcList());
        }
        // 获取今天日期
        LocalDate today = LocalDate.now();
        // 过滤掉今天以后的数据，保留今天和今天以前的数据
        List<StaLocationClock> filteredList = slc.stream().filter(s -> s.getScheduleDay().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isBefore(today.plusDays(1))).collect(Collectors.toList());
        List<StaLocationClock> clockInCollection = new ArrayList<>();
        Map<String, List<StaLocationClock>> extractedElements = new HashMap<>();
        for (StaLocationClock staLocationClock : filteredList) {
            String key = staLocationClock.getScheduleDay() + "-" + staLocationClock.getShiftCode() + "-" + staLocationClock.getStaOrderId();
            extractedElements.computeIfAbsent(key, k -> new ArrayList<>()).add(staLocationClock);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (List<StaLocationClock> extractedList : extractedElements.values()) {
            StaLocationClock clock = new StaLocationClock();
            for (StaLocationClock extractedElement : extractedList) {
                if (StringUtils.isEmpty(clock.getStaOrderId())) {
                    BeanUtils.copyProperties(extractedElement, clock);
                }
                if (StringUtils.isEmpty(clock.getClockInTime1())) {
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime1("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime1(time.format(formatter));
                    }
                    clock.setClockInStatus1(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime2())) {
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime2("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime2(time.format(formatter));
                    }
                    clock.setClockInStatus2(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime3())) {
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime3("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime3(time.format(formatter));
                    }
                    clock.setClockInStatus3(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime4())) {
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime4("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime4(time.format(formatter));

                    }
                    clock.setClockInStatus4(extractedElement.getStateFlag());
                }
            }
            clockInCollection.add(clock);
        }
        /**
         * 补充无排班
         */
        for (StaLocationClock staLocationClock : clockInCollection) {
            if (staLocationClock.getClockInTime3() == null) {
                staLocationClock.setClockInTime3("无排班");
                staLocationClock.setClockInStatus3(CommonConstant.CLOCK_STATE_FLAG_8);
            }
            if (staLocationClock.getClockInTime4() == null) {
                staLocationClock.setClockInTime4("无排班");
                staLocationClock.setClockInStatus4(CommonConstant.CLOCK_STATE_FLAG_8);
            }
        }
        //排序
        Comparator<StaLocationClock> comparator = Comparator.comparing(StaLocationClock::getRealName) //按照realName排序
                .thenComparing(StaLocationClock::getScheduleDay); //如果realName相同，按照scheduleDay排序
        Collections.sort(clockInCollection, comparator);
        String staOrderId = clockInCollection.get(0).getStaOrderId();
        StaOrder staOrder = staOrderService.getById(staOrderId);
        //填充ID只是为了网页前端签到信息单个或多个选择，并无其他作用
        for (StaLocationClock staLocationClock : clockInCollection) {
            staLocationClock.setId(UUID.randomUUID().toString());
            if (!staOrder.getStoreNo().isEmpty()) {
                staLocationClock.setStoreNo(staOrder.getStoreNo());
            }
        }
        List<AttendanceExport> attendanceExports = new ArrayList<>();
        clockInCollection.forEach(list -> {
            AttendanceExport attendanceExport = new AttendanceExport();
            BeanUtils.copyProperties(list, attendanceExport);
            attendanceExports.add(attendanceExport);
        });
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "考勤列表");
        mv.addObject(NormalExcelConstants.CLASS, AttendanceExport.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("考勤数据", "导出人:" + sysUser.getRealname(), "考勤"));
        mv.addObject(NormalExcelConstants.DATA_LIST, attendanceExports);
        return mv;
    }

    /**
     * @param request
     * @param sc
     * @param realName
     * @param userIdCard
     * @param dateRange
     * @param stateFlag
     * @param selecteddeparts
     * @return
     */
    @RequestMapping(value = "/exportXlsAttendanceExportAll")
    public ModelAndView exportXlsAttendanceExportAll(HttpServletRequest request, StaSchedule sc, @RequestParam(name = "realName", required = false) String realName, @RequestParam(name = "userIdCard", required = false) String userIdCard, @RequestParam(name = "dateRange", required = false) String dateRange, @RequestParam(name = "stateFlag", required = false) String stateFlag, @RequestParam(name = "deptName", required = false) String deptName, @RequestParam(name = "selecteddeparts", required = false) String selecteddeparts, @RequestParam(name = "businessDivisionName", required = false) String businessDivisionName, @RequestParam(name = "regionName", required = false) String regionName

    ) throws ParseException {
        // 组装查询条件查询数据
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<StaLocationClock> clockInCollection = staOrderService.exportXlsAttendanceExportAll(businessDivisionName, regionName, realName, userIdCard, dateRange, stateFlag, deptName, selecteddeparts);
        List<AttendanceExport> attendanceExports = new ArrayList<>();
        clockInCollection.forEach(list -> {
            if (WxlConvertUtils.isNotEmpty(list.getCompanyName())) {
                int index = list.getCompanyName().indexOf("(");
                if (index != -1) {
                    list.setCompanyName(list.getCompanyName().substring(0, index));
                }
            }
            list.setClockInTime1((WxlConvertUtils.isEmpty(list.getClockInTime1()) || list.getClockInTime1().equals("无") ? null : list.getClockInTime1().substring(11, 16)));
            list.setClockInTime2((WxlConvertUtils.isEmpty(list.getClockInTime2()) || list.getClockInTime2().equals("无") ? null : list.getClockInTime2().substring(11, 16)));
            list.setClockInTime3((WxlConvertUtils.isEmpty(list.getClockInTime3()) || list.getClockInTime3().equals("无") ? null : list.getClockInTime3().substring(11, 16)));
            list.setClockInTime4((WxlConvertUtils.isEmpty(list.getClockInTime4()) || list.getClockInTime4().equals("无") ? null : list.getClockInTime4().substring(11, 16)));
            AttendanceExport attendanceExport = new AttendanceExport();
            BeanUtils.copyProperties(list, attendanceExport);
            attendanceExport.setClockInStatus1(WxlConvertUtils.isNotEmpty(list.getClockInStatus1()) ? this.clockStatus(list.getClockInStatus1()) : "无");
            attendanceExport.setClockInStatus2(WxlConvertUtils.isNotEmpty(list.getClockInStatus2()) ? this.clockStatus(list.getClockInStatus2()) : "无");
            attendanceExports.add(attendanceExport);
        });
        //导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务执行列表");
        mv.addObject(NormalExcelConstants.CLASS, AttendanceExport.class);
        ExportParams exportParams = new ExportParams("任务执行列表数据", "导出人:" + sysUser.getRealname(), "任务执行");
        //exportParams.setImageBasePath("opt/staffing/upload");
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, attendanceExports);
        return mv;
    }

    public String clockStatus(Integer status) {
        Map<Integer, String> stringMap = new HashMap<Integer, String>() {{
            put(0, "无");
            put(1, "正常");
            put(2, "迟到");
            put(3, "早退");
            put(4, "超出签到范围,签到");
            put(5, "超出签到范围，迟到");
            put(6, "超出签到范围，早退");
            put(7, "补卡");
            put(8, "无排班签到");
        }};
        return stringMap.get(status);
    }

    @AutoLog(value = "任务单-已阅读入职协议")
    @ApiOperation(value = "任务单-已阅读入职协议", notes = "任务单-已阅读入职协议")
    @PutMapping(value = "/readNotice")
    public Result<?> readNotice(@RequestBody StaOrderPage staOrderPage) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        StaOrder staOrderEntity = staOrderService.getById(staOrderPage.getId());
        if (WxlConvertUtils.isEmpty(staOrderEntity)) {
            return Result.error("未找到对应数据");
        }
        if (WxlConvertUtils.isEmpty(staOrderEntity.getStaNoticeId()) && !staOrderEntity.getStaNoticeId().equals(staOrderPage.getStaNoticeId())) {
            return Result.error("未找到对应数据");
        } else {
            staOrderEntity.setNoticeReadFlag(0);
            staOrderService.updateById(staOrderEntity);
            return Result.OK("编辑成功!");
        }
    }


    /**
     * 删除任务单
     *
     * @param so
     * @return
     */
    @AutoLog(value = "任务单-删除任务单")
    @ApiOperation(value = "任务单-删除任务单", notes = "任务单-删除任务单")
    @PutMapping(value = "/deleteOrder")
    public Result<?> deleteOrder(@RequestBody StaOrder so) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(so) || WxlConvertUtils.isEmpty(so.getId())) {
            return Result.error("未获取到任务单");
        }
        StaOrder staOrder = staOrderService.getById(so.getId());
        if (WxlConvertUtils.isEmpty(staOrder)) {
            return Result.error("未找到对应数据");
        }
        if (!(CommonConstant.ORDER_STATUS_0.equals(staOrder.getStateFlag()) || CommonConstant.ORDER_STATUS_1.equals(staOrder.getStateFlag()) || CommonConstant.ORDER_STATUS_6.equals(staOrder.getStateFlag()) || CommonConstant.ORDER_STATUS_7.equals(staOrder.getStateFlag()) || CommonConstant.ORDER_STATUS_9.equals(staOrder.getStateFlag()) || CommonConstant.ORDER_STATUS_11.equals(staOrder.getStateFlag())

        )) {
            return Result.error("该任务单状态不能删除！");
        }
        staOrderService.getBaseMapper().update(null,
                new UpdateWrapper<StaOrder>().eq("id", staOrder.getId())
                        .set("del_flag", CommonConstant.DEL_FLAG_1).set("update_time",new Date()).set("update_by",user.getUsername()));
        return Result.OK("已删除!", "已删除!");
    }


    /**
     * 取消任务单
     *
     * @param so
     * @return
     */
    @AutoLog(value = "任务单-取消任务单")
    @ApiOperation(value = "任务单-取消任务单", notes = "任务单-取消任务单")
    @PutMapping(value = "/cancelOrder")
    public Result<?> cancelOrder(@RequestBody StaOrder so) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(so) || WxlConvertUtils.isEmpty(so.getId())) {
            return Result.error("未获取到任务单");
        }
        StaOrder staOrder = staOrderService.getById(so.getId());
        if (WxlConvertUtils.isEmpty(staOrder)) {
            return Result.error("未找到对应数据");
        }
        if (!CommonConstant.ORDER_STATUS_2.equals(staOrder.getStateFlag())) {
            return Result.error("只能取消申请中的任务单！");
        }
        staOrder.setUpdateBy(user.getUsername());
        staOrder.setUpdateTime(new Date());
        staOrderService.cancelOrder(staOrder);
        //发送系统通知给招聘官
        try {
            if (WxlConvertUtils.isNotEmpty(user.getBindUserId())) {
                SysUser sysUser = sysUserService.getById(user.getBindUserId());
                if (WxlConvertUtils.isNotEmpty(sysUser)) {
                    SysAnnouncement sysAnnouncement = new SysAnnouncement();
                    sysAnnouncement.setUserIds(sysUser.getId() + ",");//推送用户
                    String msgAbstract = user.getRealname() + user.getPhone() + ",撤销了<" + staOrder.getWorkName() + ">报名人：" + staOrder.getEnrollName() + staOrder.getEnrollPhone();
                    sysAnnouncement.setMsgAbstract(msgAbstract);
                    String title = "绑定用户撤销申请";
                    sysAnnouncement.setTitile(title);
                    sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                    sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                    sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
                    sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                    sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                    sysAnnouncement.setSendTime(new Date());
                    sysAnnouncement.setSender(user.getRealname());
                    sysAnnouncement.setCreateBy(user.getUsername());
                    sysAnnouncement.setCreateTime(new Date());
                    sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                }
            }
        } catch (Exception e) {
        }
        return Result.OK("已取消!", "已取消!");
    }

    /**
     * 签到信息分页列表查询
     *
     * @param staLocationClock
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "签到信息-分页列表查询")
    @ApiOperation(value = "签到信息-分页列表查询", notes = "签到信息-分页列表查询")
    @GetMapping(value = "/lclist")
    public Result<?> queryPageLocationClockList(StaLocationClock staLocationClock, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "oid", required = false) String oid, HttpServletRequest req) {
        //排班数据
        List<StaSchedule> staSchedule = staScheduleService.list(new QueryWrapper<StaSchedule>().in("sta_order_id", oid));
        QueryWrapper<StaLocationClock> queryWrapper = QueryGenerator.initQueryWrapper(staLocationClock, req.getParameterMap());
        Page<StaLocationClock> page = new Page<StaLocationClock>(pageNo, pageSize);
        queryWrapper.in("sta_order_id", oid);
        //签到数据
        IPage<StaLocationClock> pageList = staLocationClockService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * @description: 签到
     * @date 2021/11/23 23:30
     * @version 1.0
     */
    @AutoLog(value = "任务单-签到")
    @ApiOperation(value = "任务单-签到", notes = "任务单-签到")
    @PostMapping(value = "/rollCall")
    public Result<StaLocationClock> rollCall(@RequestBody StaLocationClock staLocationClock) throws ParseException {
        Result<StaLocationClock> result = new Result<StaLocationClock>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (0 != user.getId().compareTo(staLocationClock.getSysUserId())) {
            return result.error500("签到失败用户不同");
        }
        StaWork staWork = staWorkService.getById(staLocationClock.getStaWorkId());
        if (WxlConvertUtils.isEmpty(staWork)) {
            return result.error500("任务为空");
        }
        StaOrder staOrder = staOrderService.getById(staLocationClock.getStaOrderId());
        if (WxlConvertUtils.isEmpty(staOrder)) {
            return result.error500("任务单为空");
        }
        if (0 != staWork.getId().compareTo(staOrder.getStaWorkId())) {
            return result.error500("任务或任务单不匹配");
        }
//        if (CommonConstant.DEL_FLAG_1.equals(staWork.getDelFlag()) || CommonConstant.DEL_FLAG_1.equals(staOrder.getDelFlag())) {
//            return result.error500("任务或任务单作废");
//        }
//        if (!CommonConstant.WORK_STATUS_3.equals(staWork.getStateFlag()) || !CommonConstant.ORDER_STATUS_3.equals(staOrder.getStateFlag())) {
//            return result.error500("任务或任务单状态不为任务中已不能签到");
//        }
        if (staWork.getLcNeedFlag() == 0 || staOrder.getLcStateFlag() == 0) {
            return result.error500("任务或任务单无需签到");
        }
        if (WxlConvertUtils.isEmpty(staLocationClock.getLcAddressLat()) || WxlConvertUtils.isEmpty(staLocationClock.getLcAddressLng())) {
            return result.error500("获取位置失败,请重试");
        }
        //补充部门
        if (WxlConvertUtils.isNotEmpty(staLocationClock.getStaWorkId())) {
            StaWork work = staWorkService.getById(staLocationClock.getStaWorkId());
            staLocationClock.setCompanyId(work.getCompanyId());
        }
        staLocationClock.setCreateTime(new Date());
        staLocationClock.setCreateBy(user.getUsername());
        staLocationClock.setAddress(staOrder.getAddressName());
        staLocationClock.setAddressLat(staOrder.getAddressLat());
        staLocationClock.setAddressLng(staOrder.getAddressLng());
        staLocationClock.setTime(null);
        StaLocationClock locationClock = staLocationClockService.rollCall(staLocationClock, staOrder);
        if (!WxlConvertUtils.isNotEmpty(locationClock)) {
            result.setSuccess(false);
            result.setCode(201);
            return result;
        }
        if (locationClock.getStatus() != null && locationClock.getStatus() == 1) {
            result.setMessage("签到已存在,请勿重复签到");
            result.setSuccess(true);
            result.setResult(locationClock);
            return result;
        }
        result.setMessage("签到成功");
        result.setSuccess(true);
        result.setResult(locationClock);
        return result;
    }

    /**
     * 获取缺卡信息或签到记录
     *
     * @param request
     * @return
     */
    @GetMapping("/getRepairClockInfo")
    public Result getRepairClockInfo(HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        SimpleDateFormat sb = new SimpleDateFormat("HH:mm:ss");
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionClockin = userPermissionSet.contains("staffing:cardReplacement");//查询所有任务列表的权限
            if (positionClockin) {
                String cdf = request.getParameter("cdf");
                String oid = request.getParameter("oid");
                String type = request.getParameter("type");//0: 缺卡信息 1: 获取签到记录
                //排班集合
                List<StaSchedule> staSchedules = staScheduleService.list(new LambdaQueryWrapper<StaSchedule>()
                        .eq(StaSchedule::getStaOrderId, oid).eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .eq(StaSchedule::getScheduleDay, cdf)
                        .orderByAsc(StaSchedule::getStartTime));
                //签到集合
                List<StaLocationClock> staLocationClocks = staLocationClockService.list(new LambdaQueryWrapper<StaLocationClock>()
                        .eq(StaLocationClock::getStaOrderId, oid)
                        .eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .likeRight(StaLocationClock::getTimeExpect, cdf)
                        .orderByAsc(StaLocationClock::getTimeExpect));
                List<lcDayPage> lcDayPages = new ArrayList<>();
                List<lcDayPage> ldp = new ArrayList<>();
                for (StaSchedule staSchedule : staSchedules) {
                    if (staSchedule.getStartTime() != null) {
                        lcDayPage lcDayPage = new lcDayPage();
                        lcDayPage.setScheduleId(staSchedule.getId());
                        lcDayPage.setTimeType(CommonConstant.CLOCK_TYPE_1);//签到
                        lcDayPage.setTimeExpect(staSchedule.getStartTime());
                        lcDayPage.setLcList(new StaLocationClock());
                        lcDayPage.setSysUserId(staSchedule.getSysUserId());
                        lcDayPage.setUserIdCard(staSchedule.getUserIdCard());
                        lcDayPage.setRealName(staSchedule.getRealName());
                        lcDayPage.setScheduleDay(staSchedule.getScheduleDay());
                        lcDayPage.setStaOrderId(staSchedule.getStaOrderId());
                        lcDayPage.setShiftCode(staSchedule.getCode());
                        ldp.add(lcDayPage);
                    }
                    if (staSchedule.getEndTime() != null) {
                        lcDayPage lcDayPage = new lcDayPage();
                        lcDayPage.setScheduleId(staSchedule.getId());
                        lcDayPage.setTimeType(CommonConstant.CLOCK_TYPE_2);//签退
                        lcDayPage.setTimeExpect(staSchedule.getEndTime());
                        lcDayPage.setLcList(new StaLocationClock());
                        lcDayPage.setSysUserId(staSchedule.getSysUserId());
                        lcDayPage.setUserIdCard(staSchedule.getUserIdCard());
                        lcDayPage.setRealName(staSchedule.getRealName());
                        lcDayPage.setScheduleDay(staSchedule.getScheduleDay());
                        lcDayPage.setStaOrderId(staSchedule.getStaOrderId());
                        lcDayPage.setShiftCode(staSchedule.getCode());
                        ldp.add(lcDayPage);
                    }
                }
                for (com.byun.modules.staffing.vo.lcDayPage dayPage : ldp) {
                    if (!staLocationClocks.isEmpty()) {
                        Iterator<StaLocationClock> iterator = staLocationClocks.iterator();
                        while (iterator.hasNext()) {
                            StaLocationClock staLocationClock = iterator.next();
                            if (dayPage.getTimeType().equals(staLocationClock.getTimeType())
                                && sb.format(dayPage.getTimeExpect()).equals(sb.format(staLocationClock.getTimeExpect()))
                            ) {
                                staLocationClock.setScheduleDay(dayPage.getScheduleDay());
                                staLocationClock.setShiftCode(dayPage.getShiftCode());
                                dayPage.setLcList(staLocationClock);
                                iterator.remove();
                            } else {
                                lcDayPages.add(dayPage);
                                continue;
                            }
                            lcDayPages.add(dayPage);
                            break;
                        }
                    } else {
                        lcDayPages.add(dayPage);
                    }
                }
                ldp = new ArrayList<>();
                if (type.equals("0")) {
                    lcDayPages = lcDayPages.stream().filter(e -> e.getLcList().getId() == null || e.getLcList().getId().equals("")).collect(Collectors.toList());
                    for (int i = 0; i < lcDayPages.size(); i++) {
                        //缺卡记录
                        StaLocationClock staLocationClock = new StaLocationClock();
                        staLocationClock.setTimeExpect(lcDayPages.get(i).getTimeExpect());//要求签到时间
                        staLocationClock.setStaOrderId(lcDayPages.get(i).getStaOrderId());//订单id
                        staLocationClock.setTimeType(lcDayPages.get(i).getTimeType());//缺卡的是签到或签退
                        staLocationClock.setScheduleDay(lcDayPages.get(i).getScheduleDay());
                        staLocationClock.setStaScheduleId(lcDayPages.get(i).getScheduleId());
                        staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_0);
                        staLocationClock.setShiftCode(lcDayPages.get(i).getShiftCode());
                        lcDayPages.get(i).setLcList(staLocationClock);
                    }
                } else if (type.equals("1")) {
                    lcDayPages = lcDayPages.stream().filter(e -> e.getLcList().getId() != null && !e.getLcList().getId().equals("")).collect(Collectors.toList());
                }
                List<StaLocationClock> slc = new ArrayList<>();
                for (lcDayPage lcd : lcDayPages) {
                    StaLocationClock lcList = lcd.getLcList();
                    lcList.setRealName(lcd.getRealName());
                    lcList.setUserIdCard(lcd.getUserIdCard());
                    lcList.setSysUserId(lcd.getSysUserId());
                    lcd.setLcList(lcList);
                    slc.add(lcd.getLcList());
                }
                if (slc.size() < 0 || slc.isEmpty()) {
                    return Result.error(type.equals("0") ? "无信息！" : "无签到记录");
                }
                List<StaLocationClock> collect = slc.stream().distinct().collect(Collectors.toList());//去重
                return Result.OK(collect);
            } else {
                return Result.error("暂无权限!");
            }
        } else {
            return Result.error("获取部门失败!");
        }
    }

    /**
     * 网页批量补卡
     *
     * @param cardReplacementVos
     * @return
     */
    @PostMapping("/pcRepairClock")
    public Result pcRepairClock(@RequestBody List<CardReplacementVo> cardReplacementVos) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = user.getUsername();//创建人
        String oid = cardReplacementVos.get(0).getOid();
        if (oid == null || oid.length() < 0) {
            return Result.error("订单丢失！");
        }
        StaOrder staorder = staOrderService.getById(oid);
        StaWork staWork = staWorkService.getById(staorder.getStaWorkId());
        String userId = staorder.getSysUserId();//用户id
        String firmId = staorder.getFirmId();//部门id
        List<StaLocationClock> staLocationClocks = new ArrayList<>();
        cardReplacementVos.forEach(slc -> {
            StaLocationClock staLocationClock = new StaLocationClock();
            staLocationClock.setCreateBy(username);//创建人
            staLocationClock.setCreateTime(new Date());
            staLocationClock.setUpdateBy(username);
            staLocationClock.setUpdateTime(new Date());
            String timeType = null;
            //获取排班
            if (slc.getTimeType().equals("签到")) {
                timeType = "1";
            } else if (slc.getTimeType().equals("签退")) {
                timeType = "2";
            }
            staLocationClock.setTimeType(Integer.valueOf(timeType));//签到类型
            Date date = this.DateTimeExample(slc.getCdf(), slc.getTime().substring(0, 5));//要求签到时间和补签时间
            staLocationClock.setTimeExpect(date);
            staLocationClock.setTime(date);
            staLocationClock.setAddress(staWork.getAddressName()); //任务地点
            staLocationClock.setAddressLat(staWork.getAddressLat());
            staLocationClock.setAddressLng(staWork.getAddressLng());
            staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_7);//补签类型
            staLocationClock.setStaWorkId(staWork.getId());
            staLocationClock.setStaOrderId(staorder.getId());
            staLocationClock.setSysUserId(userId);
            staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
            staLocationClock.setLcAddress(staWork.getAddressName());
            staLocationClock.setLcAddressLat(staWork.getAddressLat());
            staLocationClock.setLcAddressLng(staWork.getAddressLng());
            staLocationClock.setStaScheduleId(slc.getScheduleId());
            staLocationClock.setCompanyId(firmId);
            staLocationClock.setOrgCode(staorder.getSysOrgCode());
            staLocationClocks.add(staLocationClock);
        });
        boolean flag = staLocationClockService.saveBatch(staLocationClocks);
        if (flag) {
            return Result.ok("补签成功");
        } else {
            return Result.error("系统异常！");
        }
    }

    /**
     * 小程序补卡
     *
     * @param data
     * @return
     * @date 2023/05/08 14:52
     */
    @AutoLog(value = "任务单-补卡")
    @ApiOperation(value = "任务单-补卡", notes = "任务单-补卡")
    @PostMapping(value = "/repairClock")
    public Result repairClock(@RequestBody JSONObject data) {
        Result result = new Result();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = user.getUsername();//创建人
        String cdf = data.getString("cdf");//补卡日期
        String time = data.getString("time");//补卡时间
        if (time.length() == 19) {
            time = time.substring(11, 16);
        }
        String oid = data.getString("oid");//订单id
        String scheduleId = data.getString("scheduleId");//排班id
        Integer timetype = Integer.valueOf(data.getString("timetype"));//签到类型
        StaOrder staOrder = staOrderService.getById(oid);//订单
        if (WxlConvertUtils.isEmpty(staOrder)) {
            result.setSuccess(false);
            result.setMessage("订单丢失！");
            return result;
        }
        String staWorkId = staOrder.getStaWorkId();//工作id
        StaWork staWork = staWorkService.getById(staWorkId);
        if (WxlConvertUtils.isEmpty(staWork)) {
            result.setSuccess(false);
            result.setMessage("任务丢失！");
            return result;
        }
        String userId = staOrder.getSysUserId();//用户id
        String firmId = staOrder.getFirmId();//部门id
        StaLocationClock staLocationClock = new StaLocationClock();
        staLocationClock.setCreateBy(username);//创建人
        staLocationClock.setCreateTime(new Date());
        staLocationClock.setUpdateBy(username);
        staLocationClock.setUpdateTime(new Date());
        staLocationClock.setTimeType(timetype);//签到类型
        Date date = this.DateTimeExample(cdf, time);//要求签到时间和补卡时间
        staLocationClock.setTimeExpect(date);
        staLocationClock.setTime(date);
        staLocationClock.setAddress(staWork.getAddressName()); //工作地点
        staLocationClock.setAddressLat(staWork.getAddressLat());
        staLocationClock.setAddressLng(staWork.getAddressLng());
        staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_7);//补卡类型
        staLocationClock.setStaWorkId(staWork.getId());
        staLocationClock.setStaOrderId(staOrder.getId());
        staLocationClock.setSysUserId(userId);
        staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
        staLocationClock.setLcAddress(staWork.getAddressName());
        staLocationClock.setLcAddressLat(staWork.getAddressLat());
        staLocationClock.setLcAddressLng(staWork.getAddressLng());
        staLocationClock.setStaScheduleId(scheduleId);
        staLocationClock.setCompanyId(firmId);
        staLocationClock.setOrgCode(staOrder.getSysOrgCode());
        boolean save = staLocationClockService.save(staLocationClock);
        if (save) {
            result.setSuccess(true);
            result.setMessage("补卡成功");
            return result;
        }
        return Result.error("系统异常");
    }

    @AutoLog(value = "任务单-管理批量点名")
    @ApiOperation(value = "任务单-管理批量点名", notes = "任务单-管理批量点名")
    @PutMapping(value = "/adminBatchRollCall")
    public Result<?> adminBatchRollCall(@RequestBody StaOrderPage staOrderPage) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> orderIds = staOrderPage.getOrderIds();
        if (WxlConvertUtils.isEmpty(orderIds)) {
            return Result.error("id丢失");
        }
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        for (String id : orderIds) {
            StaOrder byId = staOrderService.getById(id);
            if (WxlConvertUtils.isEmpty(byId)) {
                return Result.error("未找到任务单：" + id);
            }
            if (!CommonConstant.ORDER_STATUS_3.equals(byId.getStateFlag())) {
                return Result.error("用户：" + byId.getEnrollName() + "，不是任务中状态不能点名");
            }
            StaWork staWork = staWorkService.getById(byId.getStaWorkId());
            if (WxlConvertUtils.isEmpty(byId)) {
                return Result.error("未找到任务：" + byId.getStaWorkId());
            }
            if (0 == staWork.getRcNeedFlag()) {
                return Result.error("包含无需点名的任务");
            }
            byId.setUpdateBy(user.getUsername());
            byId.setUpdateTime(new Date());
            staOrderList.add(byId);
        }
        if (staOrderList.size() <= 0) {
            return Result.error("任务单列表为空");
        }
        staOrderService.adminBatchRollCall(staOrderList);

        return Result.OK("点名成功!");
    }


    @AutoLog(value = "任务单-管理批量审核拒绝")
    @ApiOperation(value = "任务单-管理批量审核拒绝", notes = "任务单-管理批量审核拒绝")
    @PutMapping(value = "/adminBatchReject")
    public Result<?> adminBatchReject(@RequestBody StaOrderPage staOrderPage) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> orderIds = staOrderPage.getOrderIds();
        if (WxlConvertUtils.isEmpty(orderIds)) {
            return Result.error("id丢失");
        }
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        for (String id : orderIds) {
            StaOrder byId = staOrderService.getById(id);
            if (WxlConvertUtils.isEmpty(byId)) {
                return Result.error("未找到任务单：" + id);
            }
            if (!CommonConstant.ORDER_STATUS_2.equals(byId.getStateFlag())) {
                return Result.error("用户：" + byId.getEnrollName() + "，不是待审核状态无法拒绝");
            }
            byId.setUpdateBy(user.getUsername());
            byId.setUpdateTime(new Date());
            staOrderList.add(byId);
        }
        if (staOrderList.size() <= 0) {
            return Result.error("任务单列表为空");
        }

        staOrderService.adminBatchReject(staOrderList);
        //更新申请人数
        staWorkService.updateApplyNum(staOrderList.get(0).getStaWorkId());
        return Result.OK("审核拒绝成功!");
    }

    @AutoLog(value = "任务单-管理批量剔除已通过人员")
    @ApiOperation(value = "任务单-管理批量剔除已通过人员", notes = "任务单-管理批量剔除已通过人员")
    @PutMapping(value = "/adminBatchEliminate")
    public Result<?> adminBatchEliminate(@RequestBody StaOrderPage staOrderPage) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> orderIds = staOrderPage.getOrderIds();
        if (WxlConvertUtils.isEmpty(orderIds)) {
            return Result.error("id丢失");
        }
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        String workId = null;
        for (String id : orderIds) {
            StaOrder byId = staOrderService.getById(id);
            if (WxlConvertUtils.isEmpty(byId)) {
                return Result.error("未找到任务单：" + id);
            }
            if (WxlConvertUtils.isNotEmpty(workId)) {
                if (0 != workId.compareTo(byId.getStaWorkId())) {
                    return Result.error("存在不同任务的任务单");
                }
            } else {
                workId = byId.getStaWorkId();
            }
            if (!CommonConstant.ORDER_STATUS_5.equals(byId.getStateFlag())) {
                return Result.error("用户：" + byId.getEnrollName() + "，不是待就职状态无法剔除");
            }
            byId.setUpdateBy(user.getUsername());
            byId.setUpdateTime(new Date());
            staOrderList.add(byId);
        }
        if (staOrderList.size() <= 0) {
            return Result.error("任务单列表为空");
        }

        staOrderService.adminBatchEliminate(staOrderList);
        //剔除后重新释放需要的任务数
        StaWork staWork = staWorkService.getById(workId);
        if (WxlConvertUtils.isEmpty(staWork)) {//异常导致无法更新redis
            return Result.OK("剔除成功!");
        }
        Object workAdoptNumObject = redisUtil.get(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId());
        int size = staOrderList.size();
        if (WxlConvertUtils.isEmpty(workAdoptNumObject)) {
            redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), staWork.getAdoptNum());
        } else {
            Integer integer = Integer.valueOf(String.valueOf(workAdoptNumObject));
            redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), integer - size);
        }
        return Result.OK("剔除成功!");

    }

    /**
     * 不允许批量通过
     *
     * @param staOrderPage
     * @return
     */
    @AutoLog(value = "任务单-通过接单")
    @ApiOperation(value = "任务单-通过接单", notes = "任务单-通过接单")
    @PutMapping(value = "/adminBatchAdopt")
    public Result<?> adminBatchAdopt(@RequestBody StaOrderPage staOrderPage) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> orderIds = staOrderPage.getOrderIds();
        if (WxlConvertUtils.isEmpty(orderIds)) {
            return Result.error("id丢失");
        }
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        String workId = null;
        for (String id : orderIds) {
            StaOrder byId = staOrderService.getById(id);
            if (WxlConvertUtils.isEmpty(byId)) {
                return Result.error("未找到任务单：" + id);
            }
            if (WxlConvertUtils.isNotEmpty(workId)) {
                if (0 != workId.compareTo(byId.getStaWorkId())) {
                    return Result.error("存在不同任务的任务单");
                }
            } else {
                workId = byId.getStaWorkId();
            }
            if (!CommonConstant.ORDER_STATUS_2.equals(byId.getStateFlag())) {
                return Result.error("用户：" + byId.getEnrollName() + "，不是待审核状态无法拒绝");
            }
            byId.setUpdateBy(user.getUsername());
            byId.setUpdateTime(new Date());
            staOrderList.add(byId);
        }
        if (staOrderList.size() <= 0) {
            return Result.error("任务单列表为空");
        }
        Integer integer = null;
        StaWork staWork = staWorkService.getById(workId);
        if (WxlConvertUtils.isEmpty(staWork)) {
            return Result.error("未找到任务");
        }
        Object workAdoptNumObject = redisUtil.get(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId());
        if (WxlConvertUtils.isEmpty(workAdoptNumObject)) {
            redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), staWork.getAdoptNum());
            integer = staWork.getAdoptNum();
        } else {
            integer = Integer.valueOf(String.valueOf(workAdoptNumObject));
        }
        int size = staOrderList.size();
        if (staWork.getExpectNum() != 0 && 0 > staWork.getExpectNum().compareTo(integer + size)) {
            return Result.error("审核成功人数·超过任务需要人数");
        }
        redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), integer + size);
        try {
            for (StaOrder staOrder : staOrderList) {
                String staTaskDeductionId = staOrder.getStaTaskDateId();
                StaTaskDate staTaskDate = staTaskDateService.getById(staTaskDeductionId);
                int remainingNum = staTaskDate.getRemainingNum();
                if (remainingNum <= 0) {
                    return Result.error("审核失败! 通过人数大于预定人数");
                }
            }
            staOrderService.adminBatchAdopt(staOrderList);
            //更新申请人数
            staWorkService.updateApplyNum(staWork.getId());
            return Result.OK("审核成功!");
        } catch (Exception e) {
            Object i = redisUtil.get(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId());
            redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), Integer.valueOf(String.valueOf(i)) - size);
            return Result.error("审核失败!");
        }
    }

    /**
     * 分页列表查询
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "任务单-分页列表查询(管理员)")
    @ApiOperation(value = "任务单-分页列表查询(管理员)", notes = "任务单-分页列表查询(管理员)")
    @GetMapping(value = "/queryPageOrderList")
    public Result<?> queryPageOrderList(StaOrder staOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                        @RequestParam(name = "startDate", required = false) String startDate,
                                        @RequestParam(name = "entDate", required = false) String entDate,
                                        @RequestParam(name = "wid", required = false) String wid,
                                        @RequestParam(name = "enrollName",required = false) String enrollName,
                                        HttpServletRequest req) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            Map<String, String[]> parameterMap = req.getParameterMap();
            String[] selecteddeparts = null;//企业id
            String deptId = null;
            if (parameterMap.get("selecteddeparts") != null) {
                selecteddeparts = parameterMap.get("selecteddeparts");
                deptId = selecteddeparts[0];
            }
            QueryWrapper<StaOrder> queryWrapper = new QueryWrapper<>(); //QueryGenerator.initQueryWrapper(staOrder, req.getParameterMap());
            if (WxlConvertUtils.isNotEmpty(enrollName)) {
                queryWrapper.like("enroll_name", enrollName);
            }
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }

            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, user.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else {
                return Result.error("暂无权限");
            }
            Page<StaOrder> page = new Page<StaOrder>(pageNo, pageSize);
            if (wid != null && wid.length() > 0) {
                queryWrapper.eq("sta_work_id", wid);
            }
            if (startDate != null && entDate != null) {
                queryWrapper.between("entry_date", startDate.substring(0, 10), entDate.substring(0, 10));
            }
            if (WxlConvertUtils.isNotEmpty(deptId)) {
                SysDepart sysDepart = sysDepartService.getById(selecteddeparts[0]);
                queryWrapper.likeRight("sys_org_code", sysDepart.getOrgCode());
            }
            IPage<StaOrder> pageList = staOrderService.page(page, queryWrapper);
            if (pageList.getRecords().size() == 0) {
                return Result.OK(pageList);
            }
            //主要信息填充  年龄 性别 生日
            pageList = this.populateAgeGenderBirth(pageList);
            List<String> uids = new ArrayList<>();
            List<String> oids = new ArrayList<>();
            //年龄 性别
            pageList.getRecords().forEach(r -> {
                uids.add(r.getSysUserId());
                oids.add(r.getId());
            });
            //签到记录集合
            List<StaLocationClock> clocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().in("sys_user_id", uids).in("time_type", Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2)).eq("sta_work_id", pageList.getRecords().get(0).getStaWorkId()).eq("del_flag", CommonConstant.DEL_FLAG_0).orderByAsc("time"));
            //日时数
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
            List<StaLocationClock> dailyDataList = new ArrayList<>();
            for (StaLocationClock clock : clocks) {
                LocalDate clockDate = LocalDate.parse(clock.getTime().toString(), formatter);
                if (clockDate.equals(today)) {
                    dailyDataList.add(clock);
                }
            }
            Map<String, Double> dailyWorkingHoursMap = this.workingHours(oids, dailyDataList);
            for (Map.Entry<String, Double> entry : dailyWorkingHoursMap.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setDailyWorkingHours(entry.getValue());
                    }
                });
            }
            //当前任务累计时数
            Map<String, Double> durationMap = this.workingHours(oids, clocks);
            for (Map.Entry<String, Double> entry : durationMap.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setWorkingHours(entry.getValue());
                    }
                });
            }
            /**
             * 本周时数
             */
            Calendar calendar = Calendar.getInstance();  // 获取当前日期和时间
            calendar.setTime(new Date());
            calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek()); // 将时间调整到本周的开始时间（周一）
            List<StaLocationClock> thisWeekDataList = new ArrayList<>();// 创建结果列表
            // 遍历数据列表，判断是否在本周的时间范围内
            for (StaLocationClock data : clocks) {
                Date time = data.getTime();
                if (isSameWeek(time, calendar.getTime())) {
                    thisWeekDataList.add(data);
                }
            }
            Map<String, Double> weeklyWorkingHours = this.workingHours(oids, thisWeekDataList);
            for (Map.Entry<String, Double> entry : weeklyWorkingHours.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setWeeklyWorkingHours(entry.getValue());
                    }
                });
            }
            /**
             * 本月时数
             */
            List<StaLocationClock> workingHoursList = getThisMonthData(clocks);
            Map<String, Double> monthlyWorkingHours = this.workingHours(oids, workingHoursList);
            for (Map.Entry<String, Double> entry : monthlyWorkingHours.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setMonthlyWorkingHours(entry.getValue());
                    }
                });
            }
            //任务单状态Map
            Map<Integer, String> sMap = this.taskOrderMaps();
            //状态填充
            pageList.getRecords().forEach(order -> {
                order.setPresentStatus(sMap.get(order.getStateFlag()));
            });
            return Result.OK(pageList);
        } else {
            return Result.error("未选择登录机构");
        }
    }

    /**
     * 分页列表查询
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param request
     * @return
     */
    //@AutoLog(value = "任务单-分页列表查询(管理员)")
    @ApiOperation(value = "任务单-分页列表查询(管理员)", notes = "任务单-分页列表查询(管理员)")
    @GetMapping(value = "/listByAdmin")
    public Result<?> queryPageListByAdmin(StaOrder staOrder,
                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam(name = "wid", required = false) String wid,
                                          @RequestParam(name = "enrollName",required = false) String enrollName,

                                          HttpServletRequest request) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd");
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, request.getParameterMap());
            if (WxlConvertUtils.isNotEmpty(enrollName)) {
                queryWrapper.like("enroll_name",enrollName);
            }
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, user.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else {
                return Result.error("暂无权限");
            }
            if (wid != null && wid.length() > 0) {
                queryWrapper.eq("sta_work_id", wid);
            }
            //添加任务周期
            IPage<StaOrder> pageList = staOrderService.listByAdmin(pageNo, pageSize, queryWrapper, request);
            if (pageList.getRecords().size() <= 0) {
                return Result.OK();
            }
            Set<String> staTaskDateIdList = pageList.getRecords().stream().map(StaOrder::getStaTaskDateId).collect(Collectors.toSet());
            List<StaTaskDate> staTaskDateList = staTaskDateService.list(new LambdaQueryWrapper<StaTaskDate>().in(StaTaskDate::getId, staTaskDateIdList));
            for (StaOrder record : pageList.getRecords()) {
                for (StaTaskDate staTaskDate : staTaskDateList) {
                    if (record.getStaTaskDateId().equals(staTaskDate.getId())) {
                        record.setTaskStartDateAndEndDateStr(sb.format(staTaskDate.getTaskStartDate())+"  -  "+sb.format(staTaskDate.getTaskEndDate()));
                    }
                }
            }
            //staTaskDateId
            //获取用户当月时数
            //用户id集合
            List<String> uids = pageList.getRecords().stream().filter(st -> st.getStateFlag().equals(CommonConstant.ORDER_STATUS_2)).map(StaOrder::getSysUserId).collect(Collectors.toList());
            if (uids.isEmpty()) {
                return Result.OK(pageList);
            } else {
                //获取用户当月排班
                Map<String, String> currentMonthStartEndDates = DateUtils.getCurrentMonthStartEndDates();
                String strFirstDayOfMonth = currentMonthStartEndDates.get("strFirstDayOfMonth");
                String strLastDayOfMonth = currentMonthStartEndDates.get("strLastDayOfMonth");
                LambdaQueryWrapper<StaSchedule> scheduleLambdaQueryWrapper = new LambdaQueryWrapper<StaSchedule>().in(StaSchedule::getSysUserId, uids).eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0).ge(StaSchedule::getScheduleDay, strFirstDayOfMonth).le(StaSchedule::getScheduleDay, strLastDayOfMonth);
                List<StaSchedule> staSchedules = staScheduleService.list(scheduleLambdaQueryWrapper);
                //用户排班数据
                Map<String, List<StaSchedule>> userStaScheduleMaps = staSchedules.stream().collect(Collectors.groupingBy(StaSchedule::getSysUserId));
                //用户当月时数
                Map<String, Double> userMonthTimes = new HashMap<>();
                for (Map.Entry<String, List<StaSchedule>> entry : userStaScheduleMaps.entrySet()) {
                    String sysUserId = entry.getKey(); // key
                    List<StaSchedule> staSchedulesForUser = entry.getValue(); // value
                    Double userMonthTime = 0.0;//单个用户月时数
                    for (StaSchedule staSchedule : staSchedulesForUser) {
                        Date startTime = staSchedule.getStartTime();
                        Date endTime = staSchedule.getEndTime();
                        long startMinutes = startTime.getHours() * 60 + startTime.getMinutes();
                        long endMinutes = endTime.getHours() * 60 + endTime.getMinutes();
                        long totalMinutes = endMinutes - startMinutes;
                        long hours = totalMinutes / 60;
                        long minutes = totalMinutes % 60;
                        userMonthTime += hours + (double) minutes / 60;
                    }
                    userMonthTimes.put(sysUserId, userMonthTime);
                }
                //填充月时数时长
                for (StaOrder record : pageList.getRecords()) {
                    for (Map.Entry<String, Double> entry : userMonthTimes.entrySet()) {
                        if (record.getSysUserId().equals(entry.getKey())) {
                            record.setMonthlyWorkingHours(entry.getValue());
                            continue;
                        }
                    }
                }
                List<String> TaskDateId = new ArrayList<>();
                for (StaOrder record : pageList.getRecords()) {
                    TaskDateId.add(record.getStaTaskDateId());
                }
                List<StaTaskDate> staTaskDeductions = staTaskDateService.listByIds(TaskDateId);
                if (WxlConvertUtils.isNotEmpty(staTaskDeductions) && !staTaskDeductions.isEmpty()) {
                    for (StaOrder record : pageList.getRecords()) {
                        List<StaTaskDate> collect = staTaskDeductions.stream().filter(f -> f.getId().equals(record.getStaTaskDateId())).collect(Collectors.toList());
                        if (WxlConvertUtils.isNotEmpty(collect) && !collect.isEmpty()) {
                            record.setTaskStartDate(sb.format(collect.get(0).getTaskStartDate()));
                            record.setTaskEndDate(sb.format(collect.get(0).getTaskEndDate()));
                            record.setShiftCode(collect.get(0).getShiftCode());
                        }
                    }
                }
                return Result.OK(pageList);
            }

        } else {
            return Result.error("未选择登录机构");
        }
    }

    /**
     * 时数计算
     *
     * @param oids   id集合
     * @param clocks 签到集合
     * @return
     */
    public Map<String, Double> workingHours(List<String> oids, List<StaLocationClock> clocks) {
        ArrayList<Date> dates = new ArrayList<>();
        Map<String, List<Date>> workingHoursMaps = new HashMap<>();
        Map<String, Double> durationMap = new HashMap<>();
        for (String oid : oids) {
            for (StaLocationClock clock : clocks) {
                if (clock.getStaOrderId().equals(oid)) {
                    dates.add(clock.getTime());
                }
            }
            workingHoursMaps.put(oid, dates);
            dates = new ArrayList<>();
        }
        for (Map.Entry<String, List<Date>> entry : workingHoursMaps.entrySet()) {
            String userId = entry.getKey();
            List<Date> punchInTimes = entry.getValue();
            long totalWorkDuration = this.calculateWorkDuration(punchInTimes);
            Long hours = totalWorkDuration / 60;//小时
            Long remainingMinutes = totalWorkDuration % 60; //分钟
            String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
            double workHours = Double.parseDouble(workHoursStr);
            durationMap.put(userId, workHours);
        }
        return durationMap;
    }

    public Map<String, Double> workingHours2(List<String> uids, List<StaLocationClock> clocks) {
        ArrayList<Date> dates = new ArrayList<>();
        Map<String, List<Date>> workingHoursMaps = new HashMap<>();
        Map<String, Double> durationMap = new HashMap<>();
        for (String uid : uids) {
            for (StaLocationClock clock : clocks) {
                if (clock.getSysUserId().equals(uid)) {
                    dates.add(clock.getTime());
                }
            }
            workingHoursMaps.put(uid, dates);
            dates = new ArrayList<>();
        }
        for (Map.Entry<String, List<Date>> entry : workingHoursMaps.entrySet()) {
            String userId = entry.getKey();
            List<Date> punchInTimes = entry.getValue();
            long totalWorkDuration = this.calculateWorkDuration(punchInTimes);
            Long hours = totalWorkDuration / 60;//小时
            Long remainingMinutes = totalWorkDuration % 60; //分钟
            String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
            double workHours = Double.parseDouble(workHoursStr);
            durationMap.put(userId, workHours);
        }
        return durationMap;
    }

    /**
     * 获取本月数据
     *
     * @param dataList
     * @return
     */
    public List<StaLocationClock> getThisMonthData(List<StaLocationClock> dataList) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = cal.getTime();
        cal.add(Calendar.MONTH, 1);
        Date lastDayOfMonth = cal.getTime();
        List<StaLocationClock> staCLock = new ArrayList<>();
        for (StaLocationClock clock : dataList) {
            if (clock.getTime().compareTo(firstDayOfMonth) >= 0 && clock.getTime().compareTo(lastDayOfMonth) <= 0) {
                staCLock.add(clock);
            }
        }
        return staCLock;
    }

    /**
     * 判断是否是同一周
     *
     * @param date1
     * @param date2
     * @return
     */
    private static boolean isSameWeek(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        int week1 = cal1.get(Calendar.WEEK_OF_YEAR);
        int week2 = cal2.get(Calendar.WEEK_OF_YEAR);
        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        return week1 == week2 && year1 == year2;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param staOrder
     */
    @RequestMapping(value = "/exportXlsFromListByAdmin")
    public ModelAndView exportXlsFromListByAdmin(HttpServletRequest request, StaOrder staOrder) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
            return mv;
        }
        //只查当前登录机构下的订单
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }

            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, sysUser.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                return mv;
            }
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            return mv;
        }
        String wid = request.getParameter("wid");
        if (wid != null && wid.length() > 0) queryWrapper.eq("sta_work_id", wid);
        //Step.2 获取导出数据
        List<StaOrder> queryList = staOrderService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staOrderList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staOrderList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        //时数填充
        List<String> uids = new ArrayList<>();
        List<String> oids = new ArrayList<>();
        staOrderList.forEach(r -> {
            uids.add(r.getSysUserId());
            oids.add(r.getId());
            //身份证号码计算年龄和性别
            if (isIdCardNumberValid(r.getEnrollIdCard())) {
                if (isIdCardNumberValid(r.getEnrollIdCard())) {
                    String gender = judgeGender(r.getEnrollIdCard());
                    int age = countAge(r.getEnrollIdCard());
                    String birthFromIdCard = getBirthFromIdCard(r.getEnrollIdCard());
                    r.setEnrollAge(age);
                    r.setSex(gender);
                    r.setDateOfBirth(birthFromIdCard);//生日
                }
            }
        });
        Map<Integer, String> workStatus = this.taskOrderMaps();
        Map<String, String> degreeStatus = new HashMap<String, String>() {{
            put("0", "无");
            put("1", "小学");
            put("2", "初中");
            put("3", "中专");
            put("4", "高中");
            put("5", "大专");
            put("6", "本科");
            put("7", "硕士");
            put("8", "博士");
        }};
        /**
         * 状态/学历填充
         */
        staOrderList.forEach(order -> {
            String status = workStatus.get(order.getStateFlag());
            order.setPresentStatus(status);
            String degree = degreeStatus.get(order.getEnrollDegree());
            order.setEnrollDegree(degree);
        });
        //时数
        List<StaLocationClock> clocks = new ArrayList<>();
        int batchSize = 100; // 设置每个批次的大小
        for (int i = 0; i < uids.size(); i += batchSize) {
            List<String> batchUids = uids.subList(i, Math.min(i + batchSize, uids.size()));
            List<StaLocationClock> batchClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().in("sys_user_id", batchUids).in("time_type", Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2)).eq("sta_work_id", staOrderList.get(0).getStaWorkId()).eq("del_flag", CommonConstant.DEL_FLAG_0).orderByAsc("time"));
            clocks.addAll(batchClocks);
        }
        //任务单根据任务分组
        if (WxlConvertUtils.isNotEmpty(clocks)) {
            //拆分打卡集合(key:任务id、value:打卡数据)
            Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
            for (StaLocationClock staLocationClock : clocks) {
                String staOrderId = staLocationClock.getStaOrderId();
                if (!locationClocksMap.containsKey(staOrderId)) {
                    locationClocksMap.put(staOrderId, new ArrayList<>());
                }
                locationClocksMap.get(staOrderId).add(staLocationClock);
            }
            Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
            //根据天拆分打卡(后续几天每天时数)
            for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                Double workingHours = 0.0; //当前任务单时数
                String staOrderId = entry.getKey();
                List<StaLocationClock> value = entry.getValue();
                //key签到日期，value签到数据
                Map<Date, List<StaLocationClock>> mapByDay = value.stream().collect(Collectors.groupingBy(clock -> {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(clock.getTime());
                    cal.set(Calendar.HOUR_OF_DAY, 0);
                    cal.set(Calendar.MINUTE, 0);
                    cal.set(Calendar.SECOND, 0);
                    cal.set(Calendar.MILLISECOND, 0);
                    return cal.getTime();
                }));
                //升序排序
                for (List<StaLocationClock> sublist : mapByDay.values()) {
                    sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                }
                //时数计算
                for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                    List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                    List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());//打卡时间
                    List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());//要求打卡时间
                    long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                    long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                    if (totalWorkDuration > shiftDuration) {
                        totalWorkDuration = shiftDuration;
                    }
                    Long hours = totalWorkDuration / 60;//小时
                    Long remainingMinutes = totalWorkDuration % 60; //分钟
                    String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                    double workHours = Double.parseDouble(workHoursStr); //当天时数
                    if (workHours > 0) {
                        workingHours += workHours;
                    }
                }
                workingHoursMap.put(staOrderId, workingHours);
            }
            for (StaOrder record : staOrderList) {
                if (WxlConvertUtils.isEmpty(record.getTaskManHour()) && WxlConvertUtils.isNotEmpty(workingHoursMap.get(record.getId()))) {
                    record.setTaskManHour(workingHoursMap.get(record.getId()));
                    BigDecimal bigDecimal = new BigDecimal(workingHoursMap.get(record.getId()));
                    record.setTaskAmount(record.getHourlySalary().multiply(bigDecimal).setScale(1, RoundingMode.DOWN));
                }
            }
        }
        // Step.3 组装pageList
        List<StaOrderPageExport> pageList = new ArrayList<StaOrderPageExport>();
        for (StaOrder main : staOrderList) {
            StaOrderPageExport vo = new StaOrderPageExport();
            BeanUtils.copyProperties(main, vo);
            pageList.add(vo);
        }
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "详情列表");
        mv.addObject(NormalExcelConstants.CLASS, StaOrderPageExport.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("详情数据", "导出人:" + sysUser.getRealname(), "任务"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 导出排班excel
     */
    //@RequiresPermissions("com.byun.modules.demo:sta_schedule:exportXls")
    @RequestMapping(value = "/scheduleExportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaOrder staOrder) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
            return mv;
        }

        //只查当前登录机构下的订单
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, sysUser.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                return mv;
            }
        }
        //Step.2 获取导出数据
        List<StaOrder> queryList = staOrderService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staOrderList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staOrderList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        //Step.3 表头和数据
        List<ExcelExportEntity> entityList = new ArrayList<>();
        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
        ExcelExportEntity e1 = new ExcelExportEntity("姓名", "enroll_name");
        entityList.add(e1);
        ExcelExportEntity e2 = new ExcelExportEntity("店名", "company_name");
        entityList.add(e2);
        ExcelExportEntity e3 = new ExcelExportEntity("任务", "work_name");
        entityList.add(e3);
        List<Date> dayList = new ArrayList<>();
        // Step.3 组装数据
        for (StaOrder o : staOrderList) {
            QueryWrapper<StaSchedule> query = new QueryWrapper<>();
            query.eq("sta_order_id", o.getId());
            query.eq("del_flag", CommonConstant.DEL_FLAG_0);
            query.orderByAsc("schedule_day, start_time");
            Map<String, Object> map = new HashMap<>();
            map.put("enroll_name", o.getEnrollName());
            map.put("company_name", o.getCompanyName());
            map.put("work_name", o.getWorkName());
            List<StaSchedule> scheduleList = staScheduleService.list(query);
            for (StaSchedule s : scheduleList) {
                Date scheduleDay = s.getScheduleDay();
                String formatStringDate = DateUtil.getFormatStringDate(scheduleDay);
                if (WxlConvertUtils.isNotEmpty(scheduleDay) && !dayList.contains(scheduleDay)) {
                    if (dayList.size() == 0) {
                        dayList.add(scheduleDay);
                    } else {
                        for (int i = 1; i <= dayList.size(); i++) {
                            if (scheduleDay.compareTo(dayList.get(i - 1)) < 0) {
                                dayList.set(i - 1, scheduleDay);
                                break;
                            } else if (scheduleDay.compareTo(dayList.get(i - 1)) > 0) {
                                dayList.add(scheduleDay);
                                break;
                            }
                        }
                    }
                }
                map.put(formatStringDate, s.getCode());
            }
            dataList.add(map);
        }

        for (Date day : dayList) {
            String formatStringDate = DateUtil.getFormatStringDate(day);
            ExcelExportEntity eday = new ExcelExportEntity(formatStringDate, formatStringDate);
            entityList.add(eday);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "排班列表");
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("排班数据", "导出人:" + sysUser.getRealname(), "排班"));
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        mv.addObject(NormalExcelConstants.DATA_LIST, entityList);
        return mv;
    }

    /**
     * 分页列表查询
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "任务单-分页列表查询")
    @ApiOperation(value = "任务单-分页列表查询", notes = "任务单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(StaOrder staOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) throws ParseException {
        //增加 根据sys_user_id 或 real_user_id
        String sysUserId = staOrder.getSysUserId();
        staOrder.setSysUserId(null);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sb = new SimpleDateFormat("HH:mm");
        SimpleDateFormat sbTime = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat sdd = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, req.getParameterMap());
        if (WxlConvertUtils.isNotEmpty(sysUserId)) {
            queryWrapper.and(i -> i.eq("sys_user_id", sysUserId).or().eq("real_user_id", sysUserId));
        }
        Page<StaOrder> page = new Page<StaOrder>(pageNo, pageSize);
        IPage<StaOrder> pageList = staOrderService.page(page, queryWrapper);
        List<String> oidList = new ArrayList<>();
        if (WxlConvertUtils.isNotEmpty(pageList)) {
            for (StaOrder record : pageList.getRecords()) {
                oidList.add(record.getId());
            }
        }
        List<StaSchedule> list = new ArrayList<>();
        /**
         * 返回排班 和打卡记录
         */
        Map<String, List<StaSchedule>> staScheduleList = new HashMap<>();
        Map<String, List<StaLocationClock>> staLocationClocksList = new HashMap<>();
        if (WxlConvertUtils.isNotEmpty(staOrder.getId())) {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);//默认签到日期减去一天
            String formatStringDate = DateUtil.getFormatStringDate(calendar.getTime());//yyyy-MM-dd
            //当前订单 昨天日期 的签到时间段
            List<StaSchedule> staSchedules = this.getSchedule(staOrder.getId(), formatStringDate); //staScheduleService.list(scheduleLambdaQueryWrapper);
            if (WxlConvertUtils.isNotEmpty(staSchedules) && !staSchedules.isEmpty()) {
                StaSchedule schedule = staSchedules.get(staSchedules.size() - 1); //获取最后一组排班
                if (schedule.getStartTime().after(schedule.getEndTime())) {//跨夜
                    List<StaLocationClock> staLocationClocks = this.getStaLocationClock(staOrder.getId(), formatStringDate);
                    Date scheduleDay = schedule.getScheduleDay();//yyyy-MM-dd
                    Date scheduleStartTime = schedule.getStartTime(); //hh:mm:ss
                    Date staScheduleEndTime = schedule.getEndTime();
                    String startDate = sd.format(scheduleDay) + " " + sbTime.format(scheduleStartTime);
                    String endDate = sd.format(scheduleDay) + " " + sbTime.format(staScheduleEndTime);
                    Date st = simpleDateFormat.parse(startDate);
                    Date ed = simpleDateFormat.parse(endDate);
                    Boolean sFlag = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(st));//签到是否有效
                    Boolean sEnd = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(ed));//签退是否有效
                    if (sFlag && sEnd) { //签到签退都有效  返回当天排班和签到
                        formatStringDate = DateUtil.getFormatStringDate(new Date());
                        staSchedules = this.getSchedule(staOrder.getId(), formatStringDate);
                        List<StaLocationClock> s = this.getStaLocationClock(staOrder.getId(), formatStringDate);
                        staScheduleList.put(staOrder.getId(), staSchedules);
                        staLocationClocksList.put(staOrder.getId(), s);
                    } else if (sFlag && !sEnd) { //签到有效，签退无效
                        List<StaSchedule> yesterDaySchedule = staSchedules;//昨天排班
                        List<StaLocationClock> yesterDayLocationClocks = staLocationClocks;//昨天签到
                        staSchedules = this.getSchedule(staOrder.getId(), formatStringDate);//当天排班
                        if (WxlConvertUtils.isNotEmpty(staSchedules) && !staSchedules.isEmpty()) {
                            Date thisTime = new Date();
                            String format = sbTime.format(thisTime);
                            Date startTime = staSchedules.get(0).getStartTime();
                            if (sbTime.parse(format).after(startTime)) {
                                staLocationClocks = this.getStaLocationClock(staOrder.getId(), formatStringDate);//当太难签到记录
                                staScheduleList.put(staOrder.getId(), staSchedules);
                                staLocationClocksList.put(staOrder.getId(), staLocationClocks);
                            } else {
                                staScheduleList.put(staOrder.getId(), yesterDaySchedule);
                                staLocationClocksList.put(staOrder.getId(), yesterDayLocationClocks);
                            }
                        }
                    } else {//签到签退都无效 返回当天排班和签到
                        formatStringDate = DateUtil.getFormatStringDate(new Date());
                        staSchedules = this.getSchedule(staOrder.getId(), formatStringDate);//获取第二天排班
                        staLocationClocks = this.getStaLocationClock(staOrder.getId(), formatStringDate);
                        staScheduleList.put(staOrder.getId(), staSchedules);
                        staLocationClocksList.put(staOrder.getId(), staLocationClocks);
                    }
                } else { //非跨夜 当天
                    formatStringDate = DateUtil.getFormatStringDate(new Date());
                    staSchedules = this.getSchedule(staOrder.getId(), formatStringDate);
                    List<StaLocationClock> staLocationClocks = this.getStaLocationClock(staOrder.getId(), formatStringDate);
                    staScheduleList.put(staOrder.getId(), staSchedules);
                    staLocationClocksList.put(staOrder.getId(), staLocationClocks);
                }
            } else { //昨天无排班 返回当天
                formatStringDate = DateUtil.getFormatStringDate(new Date());
                staSchedules = this.getSchedule(staOrder.getId(), formatStringDate);
                List<StaLocationClock> staLocationClocks = this.getStaLocationClock(staOrder.getId(), formatStringDate);
                staScheduleList.put(staOrder.getId(), staSchedules);
                staLocationClocksList.put(staOrder.getId(), staLocationClocks);
            }
        }
        if (!oidList.isEmpty()) {
            /**
             * 获取最晚排班时间AND当前排班和签到数据
             */
            list = staScheduleService.list(new QueryWrapper<StaSchedule>().lambda().in(StaSchedule::getStaOrderId, oidList));
            Map<String, List<StaSchedule>> groupedByStaOrderId = list.stream().collect(Collectors.groupingBy(StaSchedule::getStaOrderId));
            for (StaOrder record : pageList.getRecords()) {
                groupedByStaOrderId.forEach((staOrderId, schedules) -> {
                    if (record.getId().equals(staOrderId)) {
                        //获取最晚的结束时间
                        Optional<StaSchedule> latestSchedule = schedules.stream().max(Comparator.comparing(StaSchedule::getScheduleDay));
                        StaSchedule schedule = latestSchedule.get();
                        String da = sd.format(schedule.getScheduleDay());
                        String ti = sb.format(schedule.getEndTime());
                        try {
                            //赋值taskEndDateAndTime
                            record.setTaskEndDateAndTime(String.valueOf(sdd.parse(da + " " + ti).getTime()));
                            if (!staScheduleList.isEmpty()) { //排班
                                record.setStaScheduleList(!staScheduleList.get(record.getId()).isEmpty() ? staScheduleList.get(record.getId()) : new ArrayList<>());
                            }
                            if (!staLocationClocksList.isEmpty()) {
                                record.setStaLocationClocks(!staLocationClocksList.get(record.getId()).isEmpty() ? staLocationClocksList.get(record.getId()) : new ArrayList<>());
                            }
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
            }
        }
        return Result.OK(pageList);
    }

    private List<StaSchedule> getSchedule(String orderId, String formatStringDate) {
        LambdaQueryWrapper<StaSchedule> staScheduleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staScheduleLambdaQueryWrapper.eq(StaSchedule::getScheduleDay, formatStringDate);
        staScheduleLambdaQueryWrapper.eq(StaSchedule::getStaOrderId, orderId);
        staScheduleLambdaQueryWrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);
        staScheduleLambdaQueryWrapper.ne(StaSchedule::getCode, "///");
        return staScheduleService.list(staScheduleLambdaQueryWrapper); //当天排班
    }

    private List<StaLocationClock> getStaLocationClock(String orderId, String formatStringDate) {
        return staLocationClockService.list(new LambdaQueryWrapper<StaLocationClock>().likeRight(StaLocationClock::getTimeExpect, formatStringDate).eq(StaLocationClock::getStaOrderId, orderId).eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0));
    }

    /**
     * 获取用户签到的任务
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping("/listOfOrdersInApplication")
    public Result listOfOrdersInApplication(StaOrder staOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd");
        String orgCOde = null;
        if (WxlConvertUtils.isNotEmpty(sysUser)) {
            orgCOde = sysUser.getOrgCode();
        } else {
            return Result.error("请先登录！");
        }
        LambdaQueryWrapper<StaOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //门店名称匹配
        if (WxlConvertUtils.isNotEmpty(staOrder.getStoreNo())) {
            lambdaQueryWrapper.eq(StaOrder::getStoreNo, staOrder.getStoreNo());
        }
        lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
        lambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.orderByDesc(StaOrder::getCreateTime);
        lambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, orgCOde);
        IPage<StaOrder> orders = staOrderService.page(new Page(pageNo, pageSize), lambdaQueryWrapper);
        if (WxlConvertUtils.isNotEmpty(orders.getRecords())) {
            Set<String> staTaskDateIdList = orders.getRecords().stream().map(StaOrder::getStaTaskDateId).collect(Collectors.toSet());
            List<StaTaskDate> staTaskDateList = staTaskDateService.list(new LambdaQueryWrapper<StaTaskDate>().in(StaTaskDate::getId, staTaskDateIdList));
            for (StaOrder record : orders.getRecords()) {
                for (StaTaskDate staTaskDate : staTaskDateList) {
                    if (record.getStaTaskDateId().equals(staTaskDate.getId())) {
                        record.setTaskStartDate(sb.format(staTaskDate.getTaskStartDate()));
                        record.setTaskEndDate(sb.format(staTaskDate.getTaskEndDate()));
                        record.setShiftCode(staTaskDate.getShiftCode());
                    }
                }
            }
        }
        return Result.OK(orders);
    }
    /**
     * 添加
     *
     * @param staWorkModel
     * @return
     */
    @AutoLog(value = "任务单-添加")
    @ApiOperation(value = "任务单-添加", notes = "任务单-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody StaWorkModel staWorkModel) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        StaEnrollInfo staEnrollInfo = staEnrollInfoService.getById(staWorkModel.getStaEnrollInfoId());
        if (WxlConvertUtils.isEmpty(staEnrollInfo)) {
            return Result.error("未获取到申请信息！");
        }
        //检测是否黑名单用户
        int count = staBlacklistService.count(new QueryWrapper<StaBlackList>().lambda().eq(StaBlackList::getIdCard, staEnrollInfo.getIdCard()).eq(StaBlackList::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (count > 0) {
            return Result.error("账户异常");
        }
        StaWork staWork = staWorkService.getById(staWorkModel.getId());
        if (WxlConvertUtils.isEmpty(staWork)) {
            return Result.error("未找到任务信息");
        }
        //获取用户月工时
        /**
        Double  monthlyWorkingHours  =  staOrderService.getMonthlyWorkingHours(user.getId());
        if (monthlyWorkingHours >= 96) {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            long expirationTime = DateUtils.calculateSecondsToLastMinuteOfMonth();
            String yearMonth = currentDate.format(formatter);
            //工时超标
            redisUtil.set(CommonConstant.MONTH_WORK_TIME_CATCH +user.getId() + "_" + yearMonth,CommonConstant.USER_MONTH_WORK_TIME_STATUS1,expirationTime);
            return Result.error("本月已达到上限，无法再次接单");
        }
         *
         */
        staEnrollInfo.setName(staWorkModel.getRealNameName()); //真实姓名
        SysUser sysUser2 = sysUserService.getById(user.getId());
        StaOrder staOrder = new StaOrder(staWork, staEnrollInfo);
        staOrder.setStaTaskDateId(staWorkModel.getStaTaskDateId());
        staOrder.setEnrollName(sysUser2.getRealNameName());
        //staOrder.setEnrollName(staWorkModel.getRealNameName());
        //关联推荐人（推荐人 id 姓名 公司id 公司名称 公司code）
        String promoterId = user.getPromoterId();//当前登录用户绑定的推广者ID
        if (WxlConvertUtils.isNotEmpty(promoterId)) {
            SysUser promoterSysUser = sysUserService.getById(promoterId);
            staOrder.setRecommenderId(promoterSysUser.getId());
            staOrder.setRecommenderName(WxlConvertUtils.isNotEmpty(promoterSysUser.getRealNameName()) ? promoterSysUser.getRealNameName() : promoterSysUser.getRealname());
            String orgCode = promoterSysUser.getOrgCode();
            if (WxlConvertUtils.isNotEmpty(orgCode)) {
                String orgCodeSubstring = orgCode.substring(0, 3);
                SysDepart sysDepart = sysDepartService.getOne(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getOrgCode, orgCodeSubstring));
                staOrder.setRecommenderDeptId(sysDepart.getId());
                staOrder.setRecommenderDeptName(sysDepart.getDepartName());
                staOrder.setRecommenderDeptOrgCode(sysDepart.getOrgCode());
            }
        }
        Integer workAdoptNum = null;
        boolean examineFlag = staWork.getExamineFlag() == 0;
        //无需审核需要保证通过人数不大于申请人数
        if (examineFlag) {
            Object workAdoptNumObject = redisUtil.get(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId());
            if (WxlConvertUtils.isEmpty(workAdoptNumObject)) {
                workAdoptNum = staWork.getAdoptNum();
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum + 1);
            } else {
                workAdoptNum = Integer.valueOf(String.valueOf(workAdoptNumObject));
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum + 1);
            }
            if (staWork.getExpectNum() != 0 && 0 >= staWork.getExpectNum().compareTo(workAdoptNum)) {
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum);
                return Result.error("该任务已满员");
            }
        }
        if (examineFlag) {
            workAdoptNum += 1;
        }
        String firmId = staWorkModel.getFirmId();
        SysDepart sysDepart = sysDepartService.getById(firmId);
        if (WxlConvertUtils.isNotEmpty(sysDepart.getStoreNo())) {
            staOrder.setStoreNo(sysDepart.getStoreNo());
        }
        Boolean bl = staOrderService.createOrder(staOrder, workAdoptNum);
        //更新申请人数
        staWorkService.updateApplyNum(staWork.getId());
        if (bl) {
            //发送系统通知给管理
            try {
                if (WxlConvertUtils.isNotEmpty(user.getBindUserId())) {
                    SysUser sysUser = sysUserService.getById(user.getBindUserId());
                    if (WxlConvertUtils.isNotEmpty(sysUser)) {
                        SysAnnouncement sysAnnouncement = new SysAnnouncement();
                        sysAnnouncement.setUserIds(sysUser.getId() + ",");//推送用户
                        String msgAbstract = user.getRealname() + user.getPhone() + ",申请了<" + staOrder.getWorkName() + ">任务,请进行审核";
                        sysAnnouncement.setMsgAbstract(msgAbstract);
                        String title = "绑定用户申请任务";
                        sysAnnouncement.setTitile(title);
                        sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                        sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                        sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
                        sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                        sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                        sysAnnouncement.setSendTime(new Date());
                        sysAnnouncement.setSender(user.getRealname());
                        sysAnnouncement.setCreateBy(user.getRealname());
                        sysAnnouncement.setCreateTime(new Date());
                        sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                    }
                }
            } catch (Exception e) {
            }
            return Result.OK("申请成功！");
        } else {
            if (examineFlag) {
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum - 1);
            }
            return Result.error("申请失败，可能满员了！");
        }
    }
    /**
     * 补录
     *
     * @param staWorkModel
     * @return
     */
    @AutoLog(value = "任务单-补录")
    @ApiOperation(value = "任务单-补录", notes = "任务单-补录")
    @PostMapping(value = "/makeup")
    public Result<?> makeup(@RequestBody StaWorkModel staWorkModel) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        StaEnrollInfo staEnrollInfo = staWorkModel.getEnrollInfo();
        if (WxlConvertUtils.isEmpty(staEnrollInfo)) {
            return Result.error("未获取到申请信息！");
        }
        LambdaQueryWrapper<StaOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaOrder::getStaWorkId, staWorkModel.getId());
        queryWrapper.eq(StaOrder::getEnrollPhone, staEnrollInfo.getPhone());
        queryWrapper.ne(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_0);
        queryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        int workEnrollCount = staOrderService.count(queryWrapper);
        if (workEnrollCount > 0) {
            return Result.error("该人员已报名，请勿重复申请");
        }
        StaWork staWork = staWorkService.getById(staWorkModel.getId());
        if (WxlConvertUtils.isEmpty(staWork)) {
            return Result.error("未找到任务信息");
        }
        StaOrder staOrder = new StaOrder(staWork, staEnrollInfo);
        staOrder.setEnrollType(CommonConstant.ENROLL_TYPE_3);//类型设置为补录
        //推荐人改为直接为所属招聘官
        if (WxlConvertUtils.isNotEmpty(staWorkModel.getRecommenderPhone())) {
            //推荐人电话
            LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysUserLambdaQueryWrapper.eq(SysUser::getPhone, staWorkModel.getRecommenderPhone());
            SysUser recruiterUser = sysUserService.getOne(sysUserLambdaQueryWrapper);
            //判断招聘官(推荐人) 是否属于任务发布的公司  TODO 任务推荐人目前按FirmId判断权限
            staOrder = addWorkRecruiterUser(recruiterUser, staOrder, staWork);
        }

        staOrder.setSysUserId(user.getId());
        staOrder.setCreateBy(user.getUsername());
        staOrder.setCreateTime(new Date());
        //如果报名类型是他人，则查找他人是否存在账户
        if (!CommonConstant.ENROLL_TYPE_1.equals(staOrder.getEnrollType())) {
            if (WxlConvertUtils.isEmpty(staOrder.getRealUserId()) && WxlConvertUtils.isNotEmpty(staOrder.getEnrollPhone())) {
                SysUser userByName = sysUserService.getUserByName(staOrder.getEnrollPhone());//查询报名人电话
                if (WxlConvertUtils.isNotEmpty(userByName)) {
                    staOrder.setRealUserId(userByName.getId());
                }
            }
        }

        Integer workAdoptNum = null;
        boolean examineFlag = staWork.getExamineFlag() == 0;
        //无需审核需要保证通过人数不大于申请人数
        if (examineFlag) {
            Object workAdoptNumObject = redisUtil.get(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId());
            if (WxlConvertUtils.isEmpty(workAdoptNumObject)) {
                workAdoptNum = staWork.getAdoptNum();
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum + 1);
            } else {
                workAdoptNum = Integer.valueOf(String.valueOf(workAdoptNumObject));
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum + 1);
            }
            if (staWork.getExpectNum() != 0 && 0 >= staWork.getExpectNum().compareTo(workAdoptNum)) {
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum);
                return Result.error("该任务已满员");
            }
        }
        if (examineFlag) {
            workAdoptNum += 1;
        }
        Boolean bl = staOrderService.createOrder(staOrder, workAdoptNum);
        //更新申请人数
        staWorkService.updateApplyNum(staWork.getId());
        if (bl) {
            //发送系统通知给招聘官
            try {
                if (WxlConvertUtils.isNotEmpty(user.getBindUserId())) {
                    SysUser sysUser = sysUserService.getById(user.getBindUserId());
                    if (WxlConvertUtils.isNotEmpty(sysUser)) {
                        SysAnnouncement sysAnnouncement = new SysAnnouncement();
                        sysAnnouncement.setUserIds(sysUser.getId() + ",");//推送用户
                        String msgAbstract = user.getRealname() + user.getPhone() + ",申请了<" + staOrder.getWorkName() + ">任务";
                        sysAnnouncement.setMsgAbstract(msgAbstract);
                        String title = "绑定用户申请任务";
                        sysAnnouncement.setTitile(title);
                        sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                        sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                        sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
                        sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                        sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                        sysAnnouncement.setSendTime(new Date());
                        sysAnnouncement.setSender(user.getRealname());
                        sysAnnouncement.setCreateBy(user.getRealname());
                        sysAnnouncement.setCreateTime(new Date());
                        sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                    }
                }
            } catch (Exception e) {
            }
            return Result.OK("申请成功！");
        } else {

            if (examineFlag) {
                redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), workAdoptNum - 1);
            }
            return Result.error("申请失败，可能满员了！");
        }
    }

    /**
     * @param recruiterUser 推荐人
     * @param staOrder      订单
     * @param staWork       任务
     * @description: 添加订单中的任务推荐人
     * @return: com.byun.modules.staffing.entity.StaOrder
     * <AUTHOR>
     * @date: 2022/1/6 15:22
     */
    private StaOrder addWorkRecruiterUser(SysUser recruiterUser, StaOrder staOrder, StaWork staWork) {
        //判断招聘官(推荐人) 是否属于任务发布的公司  TODO 任务推荐人目前按FirmId判断权限
        if (WxlConvertUtils.isNotEmpty(recruiterUser)) {
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(recruiterUser.getUsername(), staWork.getFirmId());
            boolean recruiterAll = userPermissionSet.contains("staffing:position:recruiter:all");//推荐公司下所有任务
            boolean recruiterMy = userPermissionSet.contains("staffing:position:recruiter:my");//推荐公司下被分配的任务
            if (recruiterAll) {
                staOrder.setRecommenderId(recruiterUser.getId());//推荐人id
                staOrder.setRecommenderName(recruiterUser.getRealname());//推荐人名字
            } else if (recruiterMy) {
                //查询该任务是否被分配给该招聘官
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, recruiterUser.getId());
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getStaWorkId, staWork.getId());
                int count = staWorkAgentRelService.count(staWorkAgentRelLambdaQueryWrapper);
                if (count > 0) {
                    staOrder.setRecommenderId(recruiterUser.getId());//推荐人id
                    staOrder.setRecommenderName(recruiterUser.getRealname());//推荐人名字
                }
            }
        }
        return staOrder;
    }

    /**
     * 编辑
     *
     * @param staOrderPage
     * @return
     */
    @AutoLog(value = "任务单-编辑")
    @ApiOperation(value = "任务单-编辑", notes = "任务单-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody StaOrderPage staOrderPage) {
        StaOrder staOrder = new StaOrder();
        BeanUtils.copyProperties(staOrderPage, staOrder);
        StaOrder staOrderEntity = staOrderService.getById(staOrder.getId());
        if (staOrderEntity == null) {
            return Result.error("未找到对应数据");
        }
        staOrderService.updateMain(staOrder, staOrderPage.getStaLocationClockList());
        return Result.OK("编辑成功!");
    }

    /***
     * 删除已过期的申请信息返回正常数据
     * @param oids
     * @return
     */
    @Transactional
    @GetMapping("/editOrderStateFlag")
    public Result editOrderStateFlag(@RequestParam("oids") String oids) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return Result.OK();
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "任务单-通过id删除")
    @ApiOperation(value = "任务单-通过id删除", notes = "任务单-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        staOrderService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "任务单-批量删除")
    @ApiOperation(value = "任务单-批量删除", notes = "任务单-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.staOrderService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "任务单-通过id查询")
    @ApiOperation(value = "任务单-通过id查询", notes = "任务单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        StaOrder staOrder = staOrderService.getById(id);
        if (WxlConvertUtils.isEmpty(staOrder)) return Result.error("未找到对应数据");
        StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(new QueryWrapper<StaEnrollInfo>().lambda().eq(StaEnrollInfo::getIdCard, staOrder.getEnrollIdCard()));
        if (WxlConvertUtils.isEmpty(staEnrollInfo)) {
            return Result.error("用户信息不一致");
        }
        if (WxlConvertUtils.isNotEmpty(staEnrollInfo.getWeight())) {
            staOrder.setWeight(staEnrollInfo.getWeight());
        }
        if (WxlConvertUtils.isNotEmpty(staEnrollInfo.getHeight())) {
            staOrder.setHeight(staEnrollInfo.getHeight());
        }
        if (WxlConvertUtils.isNotEmpty(staEnrollInfo.getAddressName())) {
            staOrder.setAddressName(staEnrollInfo.getAddressName());
        }
        return Result.OK(staOrder);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "签到定位时间通过主表ID查询")
    @ApiOperation(value = "签到定位时间主表ID查询", notes = "签到定位时间-通主表ID查询")
    @GetMapping(value = "/queryStaLocationClockByMainId")
    public Result<?> queryStaLocationClockListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<StaLocationClock> staLocationClockList = staLocationClockService.selectByMainId(id);
        return Result.OK(staLocationClockList);
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<StaOrderPage> list = ExcelImportUtil.importExcel(file.getInputStream(), StaOrderPage.class, params);
                for (StaOrderPage page : list) {
                    StaOrder po = new StaOrder();
                    BeanUtils.copyProperties(page, po);
                    staOrderService.saveMain(po, page.getStaLocationClockList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 根据ID获取订单
     *
     * @param oid 订单id
     * @return Result
     */
    @GetMapping("/getStaOrderById/{oid}/{uid}")
    public Result getStaOrderById(@PathVariable("oid") String oid, @PathVariable("uid") String uid) {
        Result result = new Result();
        SysUser sysUser = sysUserService.getById(uid);
        StaOrder staOrder = staOrderService.getById(oid);
        if (!WxlConvertUtils.isNotEmpty(sysUser.getIsCard())) {
            result.setSuccess(false);
            result.setCode(0);
            result.setMessage("用户未绑定银行卡!");
            return result;
        }
        if (WxlConvertUtils.isNotEmpty(staOrder)) {
            result.setSuccess(true);
            result.setMessage("操作成功！");
            result.setResult(staOrder);
        } else {
            result.setSuccess(false);
            result.setMessage("任务单异常！");
        }
        return result;
    }

    /**
     * 日期 时间 合并转换data
     *
     * @param currentDate 日期
     * @param currentTime 时间(不包含秒)
     * @return java.util.data
     */
    public Date DateTimeExample(String currentDate, String currentTime) {
        // 定义日期和时间格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        // 解析日期和时间字符串为相应的对象
        LocalDate localDate = LocalDate.parse(currentDate, dateFormatter);
        LocalTime localTime = LocalTime.parse(currentTime, timeFormatter);
        // 合并日期和时间为 LocalDateTime 对象
        LocalDateTime dateTime = LocalDateTime.of(localDate, localTime);
        // 将 LocalDateTime 转换为 java.util.Date 类型
        Date date = toDate(dateTime);
        return date;
    }

    public static Date toDate(LocalDateTime dateTime) {
        return java.util.Date.from(dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    /**
     * 任务完成
     *
     * @param jsonObject {oid,uid} 订单ID 用户 ID
     * @return
     */
    @PutMapping("/resign")
    public Result resign(@RequestBody JSONObject jsonObject) {
        Result result = new Result();
        String oid = jsonObject.getString("oid");
        String taskVoucher = jsonObject.getString("taskVoucher");
        if (WxlConvertUtils.isEmpty(taskVoucher)) {
            result.setSuccess(false);
            result.setMessage("任务凭证丢失！");
            return result;
        }
        if (oid.length() < 0 && oid == null) {
            result.setSuccess(false);
            result.setMessage("订单丢失！");
            return result;
        } else {
            StaOrder staOrder = staOrderService.getById(oid);
            staOrder.setStateFlag(CommonConstant.ORDER_STATUS_10);//任务完成
            staOrder.setTaskVoucher(taskVoucher);
            staOrderService.updateById(staOrder);
            try {
                //管理员
                SysUser userAdmin = sysUserService.getOne(new QueryWrapper<SysUser>().eq("username", staOrder.getReportLiaisonTp()));
                //用户
                SysUser user = sysUserService.getById(staOrder.getSysUserId());
                if (WxlConvertUtils.isNotEmpty(user) && WxlConvertUtils.isNotEmpty(userAdmin)) {
                    SysAnnouncement sysAnnouncement = new SysAnnouncement();
                    sysAnnouncement.setUserIds(userAdmin.getId() + ",");//推送用户
                    String msgAbstract = user.getRealname() + user.getPhone() + "任务名称:<" + staOrder.getWorkName() + ">" + "申请了任务完成,请尽快审核";
                    sysAnnouncement.setMsgAbstract(msgAbstract);
                    String title = "任务完成";
                    sysAnnouncement.setTitile(title);
                    sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                    sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                    sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
                    sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                    sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                    sysAnnouncement.setSendTime(new Date());
                    sysAnnouncement.setSender(user.getRealname());
                    sysAnnouncement.setCreateBy(user.getRealname());
                    sysAnnouncement.setCreateTime(new Date());
                    sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                    result.setSuccess(true);
                    result.setMessage("申请成功！");
                    return result;
                }
            } catch (Exception e) {
                result.setSuccess(false);
                result.setMessage("系统异常！");
                return result;
            }
            result.setSuccess(true);
            result.setMessage("申请成功！");
            return result;
        }
    }

    /**
     * 管理员获取员工异动列表
     *
     * @param pageNo
     * @param pageSize
     * @param storeNo
     * @param req
     * @return
     */
    @GetMapping("/variationList")
    public Result variationList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "storeNo", required = false) String storeNo, HttpServletRequest req) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<StaOrder> p = new Page<>(pageNo, pageSize);
        List<String> subDepids = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            subDepids = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        QueryWrapper<StaOrder> queryWrapper = new QueryWrapper<StaOrder>().eq("state_flag", CommonConstant.ORDER_STATUS_13).eq("del_flag", CommonConstant.DEL_FLAG_0);
        if (WxlConvertUtils.isNotEmpty(subDepids)) {
            queryWrapper.in("company_id", subDepids);
        }
        if (WxlConvertUtils.isNotEmpty(storeNo)) {
            queryWrapper.eq("store_no", storeNo);
        }
        queryWrapper.orderByDesc("update_time");
        Page<StaOrder> page = staOrderService.page(p, queryWrapper);
        return Result.OK(page);
    }

    /**
     * 同意异动
     *
     * @param jsonObject
     * @return
     */
    @PutMapping("/agreeVariation")
    public Result agreeVariation(@RequestBody JSONObject jsonObject) {
        Result result = new Result();
        String oid = jsonObject.getString("oid");//订单id1
        if (WxlConvertUtils.isEmpty(oid)) {
            result.setSuccess(false);
            result.setMessage("订单丢失！");
        }
        staOrderService.agreeVariation(oid);
        result.setSuccess(true);
        result.setMessage("操作成功");
        return result;
    }

    /**
     * 管理员获取员工离职列表
     *
     * @return
     */
    @GetMapping("/resignList")
    public Result<Page<StaOrder>> resignList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam(name = "storeNo") String storeNo, HttpServletRequest req) {
        Result<Page<StaOrder>> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 查询部门 和下级部门
        List<String> subDepids = null;
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            subDepids = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        Page<StaOrder> staOrderPage = new Page<>(pageNo, pageSize);
        QueryWrapper<StaOrder> queryWrapper = new QueryWrapper<StaOrder>()
                .eq("state_flag", CommonConstant.ORDER_STATUS_10)
                .orderByDesc("update_time");
        if (StringUtils.isNotEmpty(storeNo)) {
            queryWrapper.eq("store_no", storeNo);
        }
        if (subDepids != null && !subDepids.isEmpty()) {
            queryWrapper.in("company_id", subDepids);
        }
        // 执行分页查询
        Page<StaOrder> page = staOrderService.page(staOrderPage, queryWrapper);
        if (page.getRecords().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("无数据！");
            return result;
        }
        // 获取用户ID列表并查询用户信息
        List<String> uids = page.getRecords().stream()
                .map(StaOrder::getRealUserId)
                .collect(Collectors.toList());
        List<SysUser> users = sysUserService.list(new QueryWrapper<SysUser>().in("id", uids));
        if (!users.isEmpty()) {
            Map<String, String> userAvatars = users.stream()
                    .filter(sysUser -> WxlConvertUtils.isNotEmpty(sysUser.getAvatar()))
                    .collect(Collectors.toMap(SysUser::getId, SysUser::getAvatar));
            //设置用户头像
            page.getRecords().forEach(order -> {
                String avatar = userAvatars.get(order.getRealUserId());
                if (avatar != null) {
                    order.setAvatar(avatar);
                }
            });
        }
        // 返回结果
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    /**
     * 审批 任务单结束
     *
     * @param jsonObject
     * @return
     */
    @PutMapping("/agreeResign")
    public Result agreeResign(@RequestBody JSONObject jsonObject) {
        Result result = new Result();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        String oid = jsonObject.getString("oid");
        String type = jsonObject.getString("type");//0小程序 1网页
        String taskScore = jsonObject.getString("taskScore");
        if (oid == null) {
            if (!type.isEmpty() && type.equals("1")) {
                return Result.error("任务丢失");
            } else {
                result.setSuccess(false);
                result.setMessage("任务丢失！");
                return result;
            }
        }
        staOrderService.agreeResign(oid, type, taskScore);
        if (WxlConvertUtils.isNotEmpty(type) && type.equals("1")) {
            return Result.OK("操作成功");
        } else {
            result.setMessage("操作成功");
            return result;
        }
    }

    /**
     * 审批 驳回辞职
     *
     * @param jsonObject
     * @return
     */
    @PutMapping("/refuseResign")
    public Result refuseResign(@RequestBody JSONObject jsonObject) {
        Result result = new Result();
        String oid = jsonObject.getString("oid");
        String context = jsonObject.getString("context");
        String type = jsonObject.getString("type");
        if (oid == null) {
            if (!type.isEmpty() && type.equals("1")) {
                return Result.error("订单丢失");
            } else {
                result.setSuccess(false);
                result.setMessage("订单丢失！");
                return result;
            }
        }
        StaOrder staOrder = staOrderService.getById(oid);
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_3);
        staOrderService.updateById(staOrder);
        try {
            //管理员
            SysUser userAdmin = sysUserService.getOne(new QueryWrapper<SysUser>().eq("username", staOrder.getReportLiaisonTp()));
            //用户
            SysUser user = sysUserService.getById(staOrder.getSysUserId());
            if (WxlConvertUtils.isNotEmpty(user) && WxlConvertUtils.isNotEmpty(userAdmin)) {
                SysAnnouncement sysAnnouncement = new SysAnnouncement();
                sysAnnouncement.setUserIds(user.getId() + ",");//推送用户
                String msgAbstract = user.getRealname() + user.getPhone() + "工作名称:<" + staOrder.getWorkName() + ">" + "你的离职申请被拒绝原因:<" + context + ">";
                sysAnnouncement.setMsgAbstract(msgAbstract);
                String title = "离职审批";
                sysAnnouncement.setTitile(title);
                sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                sysAnnouncement.setPriority(CommonConstant.PRIORITY_F);//优先级
                sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                sysAnnouncement.setSendTime(new Date());
                sysAnnouncement.setSender(user.getRealname());
                sysAnnouncement.setCreateBy(user.getRealname());
                sysAnnouncement.setCreateTime(new Date());
                sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                result.setSuccess(true);
                result.setMessage("操作成功");
                return result;
            }
        } catch (Exception e) {
            if (!type.isEmpty() && type.equals("1")) {
                return Result.error("系统异常");
            } else {
                result.setSuccess(false);
                result.setMessage("系统异常！");
                return result;
            }
        }
        if (!type.isEmpty() && type.equals("1")) {
            return Result.OK("操作成功");
        } else {
            return result;
        }
    }

    /**
     * 获取两个时间差
     *
     * @param punchInTimes
     * @return long
     */
    private long calculateWorkDuration(List<Date> punchInTimes) {
        if (punchInTimes.size() < 2) {
            return 0;
        }
        long totalWorkDuration = 0;
        Map<Date, Long> workDurationPerDay = new HashMap<>();
        for (int i = 0; i < punchInTimes.size() - 1; i += 2) {
            Date punchInTime = punchInTimes.get(i);
            Date punchOutTime = punchInTimes.get(i + 1);
            // TODO 判断签到时间是否在同一天 丹尼斯物流有跨日考勤需要在做处理
            if (isSameDay(punchInTime, punchOutTime)) {
                long workDuration = punchOutTime.getTime() - punchInTime.getTime();
                // 记录每天的工作时长
                Date workDate = getStartOfDay(punchInTime);
                workDurationPerDay.put(workDate, workDurationPerDay.getOrDefault(workDate, 0L) + workDuration);
            }
        }
        // 计算总的工作时长
        for (long workDuration : workDurationPerDay.values()) {
            totalWorkDuration += workDuration;
        }
        // 将工作时长从毫秒转换为分钟
        totalWorkDuration /= (1000 * 60);
        return totalWorkDuration;
    }

    // 判断两个日期是否在同一天
    private boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    // 获取一天的开始时间
    private Date getStartOfDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 小程序删除单个签到记录
     *
     * @param lcId 签到id
     * @return
     */
    @GetMapping("/removeLc/{lcId}")
    public Result removeLc(@PathVariable String lcId) {

        Result result = new Result();
        boolean b = staLocationClockService.removeById(lcId); //根据签到id删除
        if (!b) {//根据排班id删除
            staLocationClockService.remove(new QueryWrapper<StaLocationClock>().eq("sta_schedule_id", lcId));
        }
        result.setSuccess(true);
        return result;
    }

    //删除签到
    @DeleteMapping("/batchRemoveLc")
    public Result batchRemoveLc(@RequestBody List<StaLocationClock> staLocationClocks) {
        if (!staLocationClocks.isEmpty()) {
            List<String> clockIds = staLocationClocks.stream().map(StaLocationClock::getId).collect(Collectors.toList());
            boolean b = staLocationClockService.removeByIds(clockIds);
            //迟到时间 早退时间
            List<StaLocationClock> staLocationClocks1 = staLocationClockService.listByIds(clockIds);
            for (StaLocationClock staLocationClock : staLocationClocks1) {
                if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)) {//签到
                    staLocationClock.setLengthOfTardiness(0);
                } else if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)) {//签退
                    staLocationClock.setEarlyLeaveDuration(0);
                }
            }
            staLocationClockService.updateBatchById(staLocationClocks1);
            if (b) {
                return Result.OK("操作成功");
            } else {
                return Result.error("操作失败！");
            }
        } else {
            return Result.error("请选择要删除的签到记录！");
        }

    }

    /**
     * 检测身份证号是否有效
     *
     * @param idCardNumber 身份证号码
     * @return true || false
     */
    public static boolean isIdCardNumberValid(String idCardNumber) {
        if (idCardNumber == null || idCardNumber.length() != 18) {
            return false;
        }
        String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\\d|3[0-1])\\d{3}[0-9Xx]$";
        if (!idCardNumber.matches(regex)) {
            return false;
        }
        // 验证校验码
        char[] idArray = idCardNumber.toCharArray();
        int[] coefficientArray = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] checkCodeArray = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (idArray[i] - '0') * coefficientArray[i];
        }
        char calculatedCheckCode = checkCodeArray[sum % 11];
        if (idArray[17] != calculatedCheckCode) {
            return false;
        }
        return true;
    }

    /**
     * 获取出生日期
     *
     * @return 返回字符串类型
     */
    public static String getBirthFromIdCard(String idCard) {
        if (idCard.length() != 18 && idCard.length() != 15) {
            return "证件号码错误";
        }
        if (idCard.length() == 18) {
            String year = idCard.substring(6).substring(0, 4);// 得到年份
            String month = idCard.substring(10).substring(0, 2);// 得到月份
            String day = idCard.substring(12).substring(0, 2);// 得到日
            return (year + "-" + month + "-" + day);
        } else if (idCard.length() == 15) {
            String year = "19" + idCard.substring(6, 8);// 年份
            String month = idCard.substring(8, 10);// 月份
            String day = idCard.substring(10, 12);// 得到日
            return (year + "-" + month + "-" + day);
        }
        return null;
    }

    /**
     * 根据身份证的号码算出当前身份证持有者的年龄
     *
     * @return
     */
    public static int countAge(String idNumber) {
        if (idNumber.length() != 18 && idNumber.length() != 15) {
            throw new IllegalArgumentException("身份证号长度错误");
        }
        String year;
        String yue;
        String day;
        if (idNumber.length() == 18) {
            year = idNumber.substring(6).substring(0, 4);// 得到年份
            yue = idNumber.substring(10).substring(0, 2);// 得到月份
            day = idNumber.substring(12).substring(0, 2);//得到日
        } else {
            year = "19" + idNumber.substring(6, 8);// 年份
            yue = idNumber.substring(8, 10);// 月份
            day = idNumber.substring(10, 12);//日
        }
        Date date = new Date();// 得到当前的系统时间
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String fyear = format.format(date).substring(0, 4);// 当前年份
        String fyue = format.format(date).substring(5, 7);// 月份
        String fday = format.format(date).substring(8, 10);//
        int age = 0;
        if (Integer.parseInt(yue) == Integer.parseInt(fyue)) {//如果月份相同
            if (Integer.parseInt(day) <= Integer.parseInt(fday)) {//说明已经过了生日或者今天是生日
                age = Integer.parseInt(fyear) - Integer.parseInt(year);
            } else {
                age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
            }
        } else {

            if (Integer.parseInt(yue) < Integer.parseInt(fyue)) {
                //如果当前月份大于出生月份
                age = Integer.parseInt(fyear) - Integer.parseInt(year);
            } else {
                //如果当前月份小于出生月份,说明生日还没过
                age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
            }
        }
        //System.out.println("age = " + age);
        return age;
    }

    /**
     * 根据身份证号判断性别
     *
     * @param idNumber
     * @return
     */
    public static String judgeGender(String idNumber) throws IllegalArgumentException {
        if (idNumber.length() != 18 && idNumber.length() != 15) {
            throw new IllegalArgumentException("身份证号长度错误");
        }
        int gender = 0;
        if (idNumber.length() == 18) {
            //如果身份证号18位，取身份证号倒数第二位
            char c = idNumber.charAt(idNumber.length() - 2);
            gender = Integer.parseInt(String.valueOf(c));
        } else {
            //如果身份证号15位，取身份证号最后一位
            char c = idNumber.charAt(idNumber.length() - 1);
            gender = Integer.parseInt(String.valueOf(c));
        }
        if (gender % 2 == 1) {
            return "男";
        } else {
            return "女";
        }
    }

    /**
     * pc排班获取人员信息
     *
     * @param request
     * @return
     */
    @GetMapping("/quickScheduling")
    public Result quickScheduling(@RequestParam(value = "rosterMonth", required = false) String rosterMonth, @RequestParam(value = "companyName", required = false) String companyName, @RequestParam(value = "enrollName", required = false) String enrollName, @RequestParam(value = "storeNo", required = false) String storeNo, HttpServletRequest request) {
        if (WxlConvertUtils.isEmpty(companyName) && WxlConvertUtils.isEmpty(enrollName)) {
            return Result.error("请选择门店,或人员姓名");
        }
        List<JSONObject> resultJson = staOrderService.quickScheduling(rosterMonth, companyName, enrollName, storeNo);
        if (resultJson.isEmpty()) {
            return Result.OK("无数据", null);
        }
        return Result.OK(resultJson);
    }

    @PostMapping("/storeChangeApplication")
    public Result storeChangeApplication(@RequestBody JSONObject data) {
        Result result = new Result();
        //异动申请
        String sysUserId = data.getString("sysUserId");
        if (WxlConvertUtils.isEmpty(sysUserId)) {
            result.setSuccess(false);
            result.setMessage("用户丢失！");
            return result;
        }
        staWorkService.storeChangeApplication(data);
        result.setSuccess(true);
        result.setMessage("操作成功");
        return result;
    }

    /**
     * 月度考勤
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param fieldTime
     * @param stateFlag
     * @param selecteddeparts
     * @param req
     * @return
     * @throws ParseException
     */
    @GetMapping("/queryAllPageListByAdmin2")
    public Result<?> queryAllPageListByAdmin2(StaOrder staOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "fieldTime", required = false) String fieldTime, @RequestParam(name = "stateFlag", required = false) String stateFlag, @RequestParam(name = "selecteddeparts", required = false) String selecteddeparts, @RequestParam(name = "deptName", required = false) String deptName, @RequestParam(name = "userName", required = false) String userName, HttpServletRequest req) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, req.getParameterMap());
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, user.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else {
                return Result.error("暂无权限");
            }
            Page<StaOrder> page = new Page<StaOrder>(pageNo, pageSize);
            queryWrapper = this.orderConditionBuilder(queryWrapper, stateFlag, selecteddeparts, deptName, userName);
            IPage<StaOrder> pageList = staOrderService.page(page, queryWrapper);
            if (pageList.getRecords().size() == 0) {
                return Result.OK(pageList);
            }
            pageList = this.populateAgeGenderBirth(pageList);
            List<String> uids = new ArrayList<>();
            List<String> oids = new ArrayList<>();
            //年龄 性别
            pageList.getRecords().forEach(r -> {
                uids.add(r.getSysUserId());
                oids.add(r.getId());
            });
            LambdaQueryWrapper<StaLocationClock> clockLambdaWrapper = new LambdaQueryWrapper();
            clockLambdaWrapper.in(StaLocationClock::getTimeType, Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2));
            clockLambdaWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
            clockLambdaWrapper.orderByAsc(StaLocationClock::getTime);
            List<StaLocationClock> clocks = staLocationClockService.list(clockLambdaWrapper);
            //自定义筛选时数
            List<StaLocationClock> filteredClocks = new ArrayList<>();
            if (WxlConvertUtils.isNotEmpty(fieldTime)) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String[] split = fieldTime.split(",");
                if (WxlConvertUtils.isNotEmpty(split[0]) && WxlConvertUtils.isNotEmpty(split[1])) {
                    Date startDate = new Date(split[0]);
                    Date endData = new Date(split[1]);

                    String format1 = format.format(startDate);
                    String format2 = format.format(endData);
                    // 日期格式化器
                    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    // 解析日期字符串为LocalDate对象
                    LocalDate startDate2 = LocalDate.parse(format1, dateFormatter);
                    LocalDate endDate = LocalDate.parse(format2, dateFormatter);
                    // 结束日期增加一天
                    endDate = endDate.plusDays(1);
                    // 将日期对象重新格式化为字符串
                    String startDateStr = startDate2.format(dateFormatter);
                    String endDateStr = endDate.format(dateFormatter);
                    filteredClocks = clocks.stream().filter(clock -> {
                        try {
                            return clock.getTime().after(format.parse(startDateStr)) && clock.getTime().before(format.parse(endDateStr));
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList());
                }
            }
            //自定义区间时数
            Map<String, Double> durationMap3 = this.workingHours2(uids, filteredClocks);
            for (Map.Entry<String, Double> entry : durationMap3.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getSysUserId())) {
                        s.setCustomizeTime(entry.getValue());
                    }
                });
            }
            // 获取本月的起始日期和结束日期
            LocalDate today = LocalDate.now();
            LocalDate firstDayOfMonth = today.withDayOfMonth(1);
            LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
// 将LocalDate转换为对应的Date对象
            Date startDate = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(lastDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());

// 使用Stream的filter方法过滤数据
            List<StaLocationClock> filteredClocks2 = clocks.stream().filter(clock -> clock.getTime().after(startDate) && clock.getTime().before(endDate)).collect(Collectors.toList());
            //月时数
            Map<String, Double> month = this.workingHours(oids, filteredClocks2);
            for (Map.Entry<String, Double> entry : month.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setMonth(entry.getValue());
                    }
                });
            }
            //当前任务累计时数
            Map<String, Double> durationMap = this.workingHours(oids, clocks);
            for (Map.Entry<String, Double> entry : durationMap.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setWorkingHours(entry.getValue());
                    }
                });
            }
            //年时数
            Map<String, Double> durationMap2 = this.workingHours2(uids, clocks);
            for (Map.Entry<String, Double> entry : durationMap2.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getSysUserId())) {
                        s.setAnnualWorkHours(entry.getValue());
                    }
                });
            }
            //任务单状态
            Map<Integer, String> sMap = this.taskOrderMaps();
            //任务单状态填充
            pageList.getRecords().forEach(order -> {
                order.setPresentStatus(sMap.get(order.getStateFlag()));
            });
            return Result.OK(pageList);
        } else {
            return Result.error("未选择登录机构");
        }
    }

    @RequestMapping("/allExportXlsFromListByAdmin2")
    public ModelAndView allExportXlsFromListByAdmin2(HttpServletRequest request, @RequestParam(name = "fieldTime", required = false) String fieldTime, @RequestParam(name = "stateFlag", required = false) String stateFlag, @RequestParam(name = "selecteddeparts", required = false) String selecteddeparts, @RequestParam(name = "deptName", required = false) String deptName, @RequestParam(name = "userName", required = false) String userName, StaOrder staOrder) throws ParseException {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
            return mv;
        }
        //只查当前登录机构下的订单
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }

            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, sysUser.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                return mv;
            }
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            return mv;
        }
        queryWrapper = this.orderConditionBuilder(queryWrapper, stateFlag, selecteddeparts, deptName, userName);
        //Step.2 获取导出数据
        List<StaOrder> queryList = staOrderService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staOrderList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staOrderList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        //时数填充
        List<String> uids = new ArrayList<>();
        List<String> oids = new ArrayList<>();
        staOrderList.forEach(r -> {
            uids.add(r.getSysUserId());
            oids.add(r.getId());
            //身份证号码计算年龄和性别
            if (r.getEnrollIdCard().length() > 0 && isIdCardNumberValid(r.getEnrollIdCard())) {
                if (isIdCardNumberValid(r.getEnrollIdCard())) {
                    String gender = judgeGender(r.getEnrollIdCard());
                    int age = countAge(r.getEnrollIdCard());
                    String birthFromIdCard = getBirthFromIdCard(r.getEnrollIdCard());
                    r.setEnrollAge(age);
                    r.setSex(gender);
                    r.setDateOfBirth(birthFromIdCard);//生日
                }
            }
        });
        Map<Integer, String> sMap = this.taskOrderMaps();

        Map<Integer, String> workStatus = this.taskOrderMaps();
        Map<String, String> degreeStatus = new HashMap<String, String>() {{
            put("0", "无");
            put("1", "小学");
            put("2", "初中");
            put("3", "中专");
            put("4", "高中");
            put("5", "大专");
            put("6", "本科");
            put("7", "硕士");
            put("8", "博士");
        }};
        /**
         * 状态/学历填充
         */
        staOrderList.forEach(order -> {
            String status = workStatus.get(order.getStateFlag());
            order.setPresentStatus(status);
            String degree = degreeStatus.get(order.getEnrollDegree());
            order.setEnrollDegree(degree);
        });
        LambdaQueryWrapper<StaLocationClock> clockLambdaWrapper = new LambdaQueryWrapper();
        clockLambdaWrapper.in(StaLocationClock::getTimeType, Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2));
        clockLambdaWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        clockLambdaWrapper.orderByAsc(StaLocationClock::getTime);
        List<StaLocationClock> clocks = staLocationClockService.list(clockLambdaWrapper);
        //自定义筛选时数
        List<StaLocationClock> filteredClocks = new ArrayList<>();
        if (WxlConvertUtils.isNotEmpty(fieldTime)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String[] split = fieldTime.split(",");
            if (WxlConvertUtils.isNotEmpty(split[0]) && WxlConvertUtils.isNotEmpty(split[1])) {
                Date startDate = new Date(split[0]);
                Date endData = new Date(split[1]);
                String format1 = format.format(startDate);
                String format2 = format.format(endData);
                filteredClocks = clocks.stream().filter(clock -> {
                    try {
                        return clock.getTime().after(format.parse(format1)) && clock.getTime().before(format.parse(format2));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toList());
            }
        }
        //System.out.println(filteredClocks);
        //自定义区间时数
        Map<String, Double> durationMap3 = this.workingHours2(uids, filteredClocks);
        for (Map.Entry<String, Double> entry : durationMap3.entrySet()) {
            staOrderList.forEach(s -> {
                if (entry.getKey().equals(s.getSysUserId())) {
                    s.setCustomizeTime(entry.getValue());
                }
            });
        }
        // 获取本月的起始日期和结束日期
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        // 将LocalDate转换为对应的Date对象
        Date startDate = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(lastDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        // 使用Stream的filter方法过滤数据
        List<StaLocationClock> filteredClocks2 = clocks.stream().filter(clock -> clock.getTime().after(startDate) && clock.getTime().before(endDate)).collect(Collectors.toList());
        //月时数
        Map<String, Double> month = this.workingHours(oids, filteredClocks2);
        for (Map.Entry<String, Double> entry : month.entrySet()) {
            staOrderList.forEach(s -> {
                if (entry.getKey().equals(s.getId())) {
                    s.setMonth(entry.getValue());
                }
            });
        }
        Map<String, Double> durationMap = this.workingHours(oids, clocks);
        for (Map.Entry<String, Double> entry : durationMap.entrySet()) {
            staOrderList.forEach(s -> {
                if (entry.getKey().equals(s.getId())) {
                    s.setWorkingHours(entry.getValue());
                }
            });
        }
        //年时数
        Map<String, Double> durationMap2 = this.workingHours2(uids, clocks);
        for (Map.Entry<String, Double> entry : durationMap2.entrySet()) {
            staOrderList.forEach(s -> {
                if (entry.getKey().equals(s.getSysUserId())) {
                    s.setAnnualWorkHours(entry.getValue());
                }
            });
        }
        // Step.3 组装pageList
        List<StaOrderPageExportAll> pageList = new ArrayList<StaOrderPageExportAll>();
        for (StaOrder main : staOrderList) {
            if (WxlConvertUtils.isNotEmpty(main.getCompanyName())) {
                int index = main.getCompanyName().indexOf("(");
                if (index != -1) {
                    main.setCompanyName(main.getCompanyName().substring(0, index));
                }
            }
            //main.setCompanyName(main.getCompanyName().substring(0,index));
            StaOrderPageExportAll vo = new StaOrderPageExportAll();
            BeanUtils.copyProperties(main, vo);
            pageList.add(vo);
        }
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "月度时数");
        mv.addObject(NormalExcelConstants.CLASS, StaOrderPageExportAll.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("月度时数", "导出人:" + sysUser.getRealname(), "月度时数"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 任务单分页获取所有订单
     *
     * @param staOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping("/queryAllPageListByAdmin")
    public Result<?> queryAllPageListByAdmin(StaOrder staOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "fieldTime", required = false) String fieldTime, @RequestParam(name = "stateFlag", required = false) String stateFlag, @RequestParam(name = "selecteddeparts", required = false) String selecteddeparts, @RequestParam(name = "deptName", required = false) String deptName, @RequestParam(name = "userName", required = false) String userName, @RequestParam(name = "userId", required = false) String userId, HttpServletRequest req) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, req.getParameterMap());
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, user.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", user.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else {
                return Result.error("暂无权限");
            }
            Page<StaOrder> page = new Page<StaOrder>(pageNo, pageSize);
            //月份筛选入职日期
            if (WxlConvertUtils.isNotEmpty(fieldTime)) {
                String[] split = fieldTime.split(",");
                if (WxlConvertUtils.isNotEmpty(split[0]) && WxlConvertUtils.isNotEmpty(split[1])) {
                    Date start = new Date(split[0]);
                    Date end = new Date(split[1]);
                    queryWrapper.between("entry_date", sd.format(start), sd.format(end));
                }
            }
            //查询条件构造
            queryWrapper = this.orderConditionBuilder(queryWrapper, stateFlag, selecteddeparts, deptName, userName);
            if (WxlConvertUtils.isNotEmpty(userId)) {
                queryWrapper.eq("sys_user_id", userId);
                queryWrapper.notIn("state_Flag", Arrays.asList(0, 2, 6, 7, 9));
            }
            IPage<StaOrder> pageList = staOrderService.page(page, queryWrapper);
            if (pageList.getRecords().size() == 0) {
                return Result.OK(pageList);
            }
            pageList = this.populateAgeGenderBirth(pageList);
            List<String> uids = new ArrayList<>();
            List<String> oids = new ArrayList<>();
            pageList.getRecords().forEach(order -> {
                uids.add(order.getSysUserId());
                oids.add(order.getId());
                //司龄 人员类别 学历
                order = this.addUserOrderFoundationInfo(order);
            });
            LambdaQueryWrapper<StaLocationClock> clockLambdaWrapper = new LambdaQueryWrapper();
            clockLambdaWrapper.in(StaLocationClock::getTimeType, Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2));
            clockLambdaWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
            clockLambdaWrapper.orderByAsc(StaLocationClock::getTime);
            List<StaLocationClock> clocks = staLocationClockService.list(clockLambdaWrapper);
            //当前任务累计时数
            Map<String, Double> durationMap = this.workingHours(oids, clocks);
            for (Map.Entry<String, Double> entry : durationMap.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getId())) {
                        s.setWorkingHours(entry.getValue());
                    }
                });
            }
            //年时数
            Map<String, Double> durationMap2 = this.workingHours2(uids, clocks);
            for (Map.Entry<String, Double> entry : durationMap2.entrySet()) {
                pageList.getRecords().forEach(s -> {
                    if (entry.getKey().equals(s.getSysUserId())) {
                        s.setAnnualWorkHours(entry.getValue());
                    }
                });
            }
            //任务单状态
            Map<Integer, String> sMap = this.taskOrderMaps();
            //任务单状态填充
            pageList.getRecords().forEach(order -> {
                order.setPresentStatus(sMap.get(order.getStateFlag()));
            });
            return Result.OK(pageList);
        } else {
            return Result.error("未选择登录机构");
        }
    }


    /**
     * 订单接口中填充 司龄 学历 人员类别
     *
     * @param staOrder
     * @return
     */
    public StaOrder addUserOrderFoundationInfo(StaOrder staOrder) {
        int companyServiceDays = staScheduleService.getCompanyServiceYears(staOrder.getId());
        staOrder.setCompanyServiceDays(companyServiceDays); //司龄
        //学历
        SysUser sysUser = sysUserService.getById(staOrder.getSysUserId());
        staOrder.setEnrollDegree(sysUser.getDegree());
        //人员类别  普通  实习生  返聘
        if (WxlConvertUtils.isNotEmpty(sysUser.getIdCard())) {
            //身份证是否合法
            boolean idCardNumberValid = isIdCardNumberValid(sysUser.getIdCard());
            if (idCardNumberValid) {
                String idCardNo = sysUser.getIdCard();
                LocalDate birthDate = LocalDate.parse(idCardNo.substring(6, 14), java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                int age = Period.between(birthDate, LocalDate.now()).getYears();
                String gender = Integer.parseInt(idCardNo.substring(16, 17)) % 2 == 1 ? "男" : "女";
                staOrder.setEnrollAge(age);
                staOrder.setSex(gender);
                if (gender.equals("男")) {
                    if (age > 60) {
                        //男返聘
                        staOrder.setStaUserType("返聘");
                        return staOrder;
                    }
                } else if (gender.equals("女")) {
                    if (age > 50) {
                        //女返聘
                        staOrder.setStaUserType("返聘");
                        return staOrder;
                    }
                }
            }
            if (WxlConvertUtils.isNotEmpty(sysUser.getDegree()) && WxlConvertUtils.isNotEmpty(sysUser.getGraduationTime())) {
                //实习生
                Date graduationTime = sysUser.getGraduationTime();
                Date date = new Date();
                if (graduationTime.compareTo(date) > 0) {
                    staOrder.setStaUserType("实习生");
                    return staOrder;
                    //未毕业  实习生
                } else {
                    //普通
                    staOrder.setStaUserType("普通");
                    return staOrder;
                }
            } else {
                //普通
                staOrder.setStaUserType("普通");
                return staOrder;
            }
        }
        return staOrder;
    }

    @RequestMapping("/allExportXlsFromListByAdmin")
    public ModelAndView allExportXlsFromListByAdmin(HttpServletRequest request, @RequestParam(name = "fieldTime", required = false) String fieldTime, @RequestParam(name = "stateFlag", required = false) String stateFlag, @RequestParam(name = "selecteddeparts", required = false) String selecteddeparts, @RequestParam(name = "deptName", required = false) String deptName, @RequestParam(name = "userName", required = false) String userName, StaOrder staOrder) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaOrder> queryWrapper = QueryGenerator.initQueryWrapper(staOrder, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
            return mv;
        }
        //只查当前登录机构下的订单
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            if (positionlist) {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else if (positionmylist) {//获取用户当前部门权限
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
                //代理人代理的任务
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, sysUser.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in("sta_work_id", widList);
                } else {
                    queryWrapper.in("sta_work_id", "isnull");
                }
                if (all) {
                } else if (priv) {
                    queryWrapper.eq("recommender_id", sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                return mv;
            }
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            return mv;
        }
        //查询条件构造
        queryWrapper = this.orderConditionBuilder(queryWrapper, stateFlag, selecteddeparts, deptName, userName);
        //Step.2 获取导出数据
        List<StaOrder> queryList = staOrderService.list(queryWrapper);
        //过滤选中数据
        String selections = request.getParameter("selections");
        List<StaOrder> staOrderList = new ArrayList<StaOrder>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staOrderList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staOrderList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        //时数填充
        List<String> uids = new ArrayList<>();
        List<String> oids = new ArrayList<>();
        Map<String, String> degreeStatus = new HashMap<String, String>() {{
            put("0", "无");
            put("1", "小学");
            put("2", "初中");
            put("3", "中专");
            put("4", "高中");
            put("5", "大专");
            put("6", "本科");
            put("7", "硕士");
            put("8", "博士");
        }};
        staOrderList.forEach(order -> {
            uids.add(order.getSysUserId());
            oids.add(order.getId());
            //身份证号码计算年龄和性别
            //司龄 人员类别 学历
            order = this.addUserOrderFoundationInfo(order);
            if (WxlConvertUtils.isNotEmpty(order.getEnrollDegree())) {
                order.setEnrollDegree(degreeStatus.get(order.getEnrollDegree()));
            }
        });
        Map<Integer, String> workStatus = this.taskOrderMaps();
        /**
         * 状态/学历填充
         */
        staOrderList.forEach(order -> {
            String status = workStatus.get(order.getStateFlag());
            order.setPresentStatus(status);
        });
        //时数
        List<StaLocationClock> clocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().in("time_type", Arrays.asList(CommonConstant.CLOCK_TYPE_1, CommonConstant.CLOCK_TYPE_2)).eq("del_flag", CommonConstant.DEL_FLAG_0).orderByAsc("time"));
        //当前任务总时数
        Map<String, Double> durationMap = this.workingHours(oids, clocks);
        for (Map.Entry<String, Double> entry : durationMap.entrySet()) {
            staOrderList.forEach(s -> {
                if (entry.getKey().equals(s.getId())) {
                    s.setWorkingHours(entry.getValue());
                }
            });
        }
        // Step.3 组装pageList
        List<StaOrderPageExportAll> pageList = new ArrayList<StaOrderPageExportAll>();
        for (StaOrder main : staOrderList) {
            int index = main.getCompanyName().indexOf("(");
            if (index != -1) {
                main.setCompanyName(main.getCompanyName().substring(0, index));
            }
            StaOrderPageExportAll vo = new StaOrderPageExportAll();
            BeanUtils.copyProperties(main, vo);
            pageList.add(vo);
        }
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "人员明细");
        mv.addObject(NormalExcelConstants.CLASS, StaOrderPageExportAll.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("人员明细", "导出人:" + sysUser.getRealname(), "人员明细"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }
    /**
     * 获取员工工作中的任务
     *
     * @param uid 用户id
     * @return
     */
    @GetMapping("/obtainingTasksFromUsersWork/{uid}")
    public Result obtainingTasksFromUsersWork(@PathVariable("uid") String uid) {
        Result result = new Result();
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staOrderLambdaQueryWrapper.eq(StaOrder::getSysUserId, uid);
        staOrderLambdaQueryWrapper.in(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_3, CommonConstant.ORDER_STATUS_13));
        staOrderLambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<StaOrder> list = staOrderService.list(staOrderLambdaQueryWrapper);
        if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
            if (list.get(0).getStateFlag().equals(CommonConstant.ORDER_STATUS_3)) { //工作中异动
                StaOrder staOrder = list.get(0);
                result.setSuccess(true);
                result.setMessage("操作成功！");
                result.setCode(200);
                result.setResult(staOrder);
                return result;
            } else {
                result.setSuccess(false);
                result.setMessage("异动流程审核中！");
                return result;
            }
        } else {
            result.setSuccess(false);
            result.setMessage("当前没有正在工作中的任务,不支持异动！");
            return result;
        }
    }

    @PutMapping("/taskGradeScore")
    public Result taskGradeScore(@RequestBody JSONObject data) {
        String sysUserId = data.getString("sysUserId");
        String firmId = data.getString("firmId");
        String staOrderId = data.getString("staOrderId");
        String workId = data.getString("workId");
        if (WxlConvertUtils.isEmpty(sysUserId)) {
            return Result.error("用户丢失");
        }
        if (WxlConvertUtils.isEmpty(firmId)) {
            return Result.error("新部门丢失");
        }
        if (WxlConvertUtils.isEmpty(staOrderId)) {
            return Result.error("订单丢失");
        }
        if (WxlConvertUtils.isEmpty(workId)) {
            return Result.error("任务丢失");
        }
        Boolean result = staOrderService.taskGradeScore(sysUserId, firmId, staOrderId, workId);
        return result ? Result.OK() : Result.error("操作失败");
    }

    /**
     * 任务类型导出
     *
     * @param orderStaFlag
     * @param request
     * @return
     */
    @RequestMapping("/orderExport")
    public ModelAndView orderExport(@RequestParam("orderStaFlag") String orderStaFlag, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "为获取到部门权限");
            return mv;
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            LambdaQueryWrapper<StaOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (positionlist) { //查询所有权限
                lambdaQueryWrapper.in(StaOrder::getCompanyId, departIdArr);
                if (all) {
                } else if (priv) { //查自己
                    lambdaQueryWrapper.eq(StaOrder::getRecommenderId, sysUser.getId());
                } else {
                    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                    mv.addObject(NormalExcelConstants.FILE_NAME, "权限不足");
                    return mv;
                }
            } else if (positionmylist) { //当前部门权限
                lambdaQueryWrapper.in(StaOrder::getCompanyId, departIdArr);
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "暂无权限");
                return mv;
            }
            String orgCode = sysUser.getOrgCode();
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (WxlConvertUtils.isNotEmpty(parameterMap.get("selecteddeparts"))) {
                String[] selecteddeparts = parameterMap.get("selecteddeparts");
                lambdaQueryWrapper.eq(StaOrder::getFirmId, selecteddeparts[0]);
            }
            if (WxlConvertUtils.isNotEmpty(parameterMap.get("deptName"))) {
                String[] deptName = parameterMap.get("deptName");
                SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, deptName[0]));
                lambdaQueryWrapper.likeRight(StaOrder::getOrderCode, sysDepart.getOrgCode());
            }
            if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_2))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
            } else if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_3))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3);
            } else if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_6))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_6);
            } else {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "系统异常");
                return mv;
            }
            lambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, orgCode);
            lambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
            lambdaQueryWrapper.orderByDesc(StaOrder::getCreateTime);
            List<StaOrder> list = staOrderService.list(lambdaQueryWrapper);
            if (list.isEmpty()) {
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "无数据");
                return mv;
            }
            for (StaOrder staOrder : list) {
                boolean idCardNumberValid = isIdCardNumberValid(staOrder.getEnrollIdCard());
                if (idCardNumberValid) {
                    String idCardNo = staOrder.getEnrollIdCard();
                    LocalDate birthDate = LocalDate.parse(idCardNo.substring(6, 14), java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                    int age = Period.between(birthDate, LocalDate.now()).getYears();
                    String gender = Integer.parseInt(idCardNo.substring(16, 17)) % 2 == 1 ? "男" : "女";
                    staOrder.setAge(age);
                    staOrder.setSex(gender);
                }

            }
            List<StaOrderStatusExportVo> staOrderStatusExportVos = new ArrayList<>();
            for (StaOrder staOrder : list) {
                StaOrderStatusExportVo staOrderStatusExportVo = new StaOrderStatusExportVo();
                BeanUtils.copyProperties(staOrder, staOrderStatusExportVo);
                staOrderStatusExportVos.add(staOrderStatusExportVo);

            }
            //list 数据列表
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "任务列表");
            mv.addObject(NormalExcelConstants.CLASS, StaOrderStatusExportVo.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务列表", "导出人:" + sysUser.getRealname(), "任务"));
            mv.addObject(NormalExcelConstants.DATA_LIST, staOrderStatusExportVos);
            return mv;
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "未获取到部门");
            return mv;
        }
    }

    /**
     * 获取类型任务
     *
     * @return
     */
    @GetMapping("/queryOrder")
    public Result queryJobApplication(@RequestParam("orderStaFlag") String orderStaFlag,
                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                      @RequestParam(name = "enrollName",required = false) String enrollName,
                                      HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
            boolean priv = userPermissionSet.contains("staffing:position:people:private");//只查自己的人
            boolean all = userPermissionSet.contains("staffing:position:people:all");//查全部
            LambdaQueryWrapper<StaOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (WxlConvertUtils.isNotEmpty(enrollName)) {
                lambdaQueryWrapper.like(StaOrder::getEnrollName,enrollName);
            }
            if (positionlist) { //查询所有权限
                lambdaQueryWrapper.in(StaOrder::getCompanyId, departIdArr);
                if (all) {
                } else if (priv) { //查自己
                    lambdaQueryWrapper.eq(StaOrder::getRecommenderId, sysUser.getId());
                } else {
                    return Result.error("权限不足");
                }
            } else if (positionmylist) { //当前部门权限
                lambdaQueryWrapper.in(StaOrder::getCompanyId, departIdArr);
            } else {
                return Result.error("暂无权限");
            }
            String orgCode = sysUser.getOrgCode();
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (WxlConvertUtils.isNotEmpty(parameterMap.get("selecteddeparts"))) {
                String[] selecteddeparts = parameterMap.get("selecteddeparts");
                SysDepart sysDepart = sysDepartService.getById(selecteddeparts[0]);
                lambdaQueryWrapper.likeRight(StaOrder::getOrderCode, sysDepart.getOrgCode());
            } else {
                if (WxlConvertUtils.isNotEmpty(parameterMap.get("deptName"))) {
                    String[] deptName = parameterMap.get("deptName");
                    SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, deptName[0]));
                    lambdaQueryWrapper.likeRight(StaOrder::getOrderCode, sysDepart.getOrgCode());
                }
            }

            if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_2))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
            } else if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_3))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3);
            } else if (orderStaFlag.equals(String.valueOf(CommonConstant.ORDER_STATUS_6))) {
                lambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_6);
            } else {
                return Result.error("系统异常");
            }
            lambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, orgCode);
            lambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
            lambdaQueryWrapper.orderByDesc(StaOrder::getCreateTime);
            IPage<StaOrder> page = new Page<>(pageNo, pageSize);
            IPage<StaOrder> pageList = staOrderService.page(page, lambdaQueryWrapper);
            for (StaOrder staOrder : pageList.getRecords()) {
                boolean idCardNumberValid = isIdCardNumberValid(staOrder.getEnrollIdCard());
                if (idCardNumberValid) {
                    String idCardNo = staOrder.getEnrollIdCard();
                    LocalDate birthDate = LocalDate.parse(idCardNo.substring(6, 14), java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                    int age = Period.between(birthDate, LocalDate.now()).getYears();
                    String gender = Integer.parseInt(idCardNo.substring(16, 17)) % 2 == 1 ? "男" : "女";
                    staOrder.setAge(age);
                    staOrder.setSex(gender);
                }
            }
            List<String> uids = pageList.getRecords().stream().filter(st -> st.getStateFlag().equals(CommonConstant.ORDER_STATUS_2)).map(StaOrder::getSysUserId).collect(Collectors.toList());
            if (uids.isEmpty()) {
                return Result.OK(pageList);
            } else {
                //获取用户当月排班
                Map<String, String> currentMonthStartEndDates = DateUtils.getCurrentMonthStartEndDates();
                String strFirstDayOfMonth = currentMonthStartEndDates.get("strFirstDayOfMonth");
                String strLastDayOfMonth = currentMonthStartEndDates.get("strLastDayOfMonth");
                LambdaQueryWrapper<StaSchedule> scheduleLambdaQueryWrapper = new LambdaQueryWrapper<StaSchedule>().in(StaSchedule::getSysUserId, uids).eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0).ge(StaSchedule::getScheduleDay, strFirstDayOfMonth).le(StaSchedule::getScheduleDay, strLastDayOfMonth);
                List<StaSchedule> staSchedules = staScheduleService.list(scheduleLambdaQueryWrapper);
                //用户排班数据
                Map<String, List<StaSchedule>> userStaScheduleMaps = staSchedules.stream().collect(Collectors.groupingBy(StaSchedule::getSysUserId));
                //用户当月时数
                Map<String, Double> userMonthTimes = new HashMap<>();
                for (Map.Entry<String, List<StaSchedule>> entry : userStaScheduleMaps.entrySet()) {
                    String sysUserId = entry.getKey(); // key
                    List<StaSchedule> staSchedulesForUser = entry.getValue(); // value
                    Double userMonthTime = 0.0;//单个用户月时数
                    for (StaSchedule staSchedule : staSchedulesForUser) {
                        Date startTime = staSchedule.getStartTime();
                        Date endTime = staSchedule.getEndTime();
                        long startMinutes = startTime.getHours() * 60 + startTime.getMinutes();
                        long endMinutes = endTime.getHours() * 60 + endTime.getMinutes();
                        long totalMinutes = endMinutes - startMinutes;
                        long hours = totalMinutes / 60;
                        long minutes = totalMinutes % 60;
                        userMonthTime += hours + (double) minutes / 60;
                    }
                    userMonthTimes.put(sysUserId, userMonthTime);
                }
                for (StaOrder record : pageList.getRecords()) {
                    for (Map.Entry<String, Double> entry : userMonthTimes.entrySet()) {
                        if (record.getSysUserId().equals(entry.getKey())) {
                            record.setMonthlyWorkingHours(entry.getValue());
                            continue;
                        }
                    }
                }
                return Result.OK(pageList);
            }
        } else {
            return Result.error("未获取到部门");
        }
    }

    /**
     * 获取申请离职,异动的人数
     *
     * @return
     */
    @GetMapping("/fetchResignationAndMovementCount")
    public Result fetchResignationAndMovementCount() {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        Map<String, Integer> resultCount = staOrderService.fetchResignationAndMovementCount(user);
        if (resultCount != null && !resultCount.isEmpty()) {
            return Result.OK(resultCount);
        } else {
            return Result.error("获取部门失败");
        }
    }

    /**
     * 人员生命周期列表
     *
     * @param pageNo       页
     * @param pageSize     条
     * @param realNameName 姓名
     * @param username     手机号码
     * @param req
     * @return Result
     * @throws ParseException
     */
    @GetMapping("/queryResignationDetails")
    public Result queryResignationDetails(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "realNameName", required = false) String realNameName, @RequestParam(name = "username", required = false) String username, @RequestParam(name = "deptNo", required = false) String deptNo, @RequestParam(name = "deptName", required = false) String deptName, HttpServletRequest req) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        IPage<SysUser> userIPage = new Page<>(pageNo, pageSize);
        userIPage = sysUserService.listTaskTotalPage(userIPage, realNameName, username, deptNo, deptName);
        if (userIPage.getRecords().isEmpty()) {
            return Result.OK(userIPage);
        }
        for (SysUser record : userIPage.getRecords()) {
            if (WxlConvertUtils.isNotEmpty(record.getIdCard())) {
                String sex = this.judgeGender(record.getIdCard());
                int age = this.countAge(record.getIdCard());
                record.setSex(sex.equals("男") ? 0 : 1);
                record.setAge(age);
            }
        }
        //获取用户id
        List<String> userIds = userIPage.getRecords().stream().map(SysUser::getId).collect(Collectors.toList());
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().lambda().eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0).isNotNull(StaLocationClock::getTimeExpect).in(StaLocationClock::getSysUserId, userIds));
        //打卡记录分组
        Map<String, List<StaLocationClock>> groupedByUserId = staLocationClocks.stream().collect(Collectors.groupingBy(StaLocationClock::getSysUserId));
        //getTimeExpect 升序排序
        groupedByUserId.forEach((userId, staLocationClockList) -> {
            Collections.sort(staLocationClockList, Comparator.comparing(StaLocationClock::getTimeExpect).thenComparing(StaLocationClock::getStaOrderId));
        });
        Map<String, Double> userStatisticalWorkingHours = new HashMap<>();//时数
        Map<String, Integer> siLeng = new HashMap<>();//司龄
        for (Map.Entry<String, List<StaLocationClock>> entry : groupedByUserId.entrySet()) {
            List<StaLocationClock> staLocationClockList = entry.getValue();
            double v = this.calculateTotalWorkHours(staLocationClockList);
            Set<String> uniqueDates = new HashSet<>();
            for (StaLocationClock clock : staLocationClockList) {
                String dateStr = dateFormat.format(clock.getTime());
                uniqueDates.add(dateStr);
            }
            siLeng.put(entry.getKey(), uniqueDates.size());
            userStatisticalWorkingHours.put(entry.getKey(), v);
        }
        Set<String> storesIdList = new TreeSet<>();
        for (SysUser record : userIPage.getRecords()) {
            if (WxlConvertUtils.isNotEmpty(record.getCurrentStoresId())) {
                storesIdList.add(record.getCurrentStoresId());
            }
            if (userStatisticalWorkingHours.get(record.getId()) != null) {
                String str = String.format("%.1f", userStatisticalWorkingHours.get(record.getId()));
                record.setStatisticalWorkingHours(WxlConvertUtils.isNotEmpty(Double.parseDouble(str)) ? Double.parseDouble(str) : 0.0);
                record.setCompanyServiceDays(siLeng.get(record.getId()));
            }
        }
        List<SysUserInfoVo> sysUserInfoVos = new ArrayList<>();
        for (SysUser record : userIPage.getRecords()) {
            SysUserInfoVo sysUserInfoVo = new SysUserInfoVo();
            BeanUtils.copyProperties(record, sysUserInfoVo);
            sysUserInfoVo.setSex(String.valueOf(record.getSex()));
            sysUserInfoVos.add(sysUserInfoVo);
        }
        List<SysDepart> storesList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, storesIdList)); //门店集合
        Set<String> deptRegionIdList = storesList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());
        List<SysDepart> deptRegionList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, deptRegionIdList));
        Set<String> deptBusinessDivisionIdList = deptRegionList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());
        List<SysDepart> deptBusinessDivisionList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, deptBusinessDivisionIdList));
        for (SysUserInfoVo sysUserInfoVo : sysUserInfoVos) {
            List<SysDepart> collect = storesList.stream().filter(s -> s.getId().equals(sysUserInfoVo.getCurrentStoresId())).collect(Collectors.toList());
            if (WxlConvertUtils.isNotEmpty(collect) && !collect.isEmpty()) {
                sysUserInfoVo.setStoreNo(collect.get(0).getStoreNo());
                String s = collect.get(0).getParentId();
                List<SysDepart> collect1 = deptRegionList.stream().filter(d -> d.getId().equals(s)).collect(Collectors.toList()); //区域
                sysUserInfoVo.setRegionName(collect1.get(0).getDepartName()); //区域
                String parentId = collect1.get(0).getParentId();
                List<SysDepart> collect2 = deptBusinessDivisionList.stream().filter(a -> a.getId().equals(parentId)).collect(Collectors.toList());
                sysUserInfoVo.setBusinessDivisionName(collect2.get(0).getDepartName());
            }
        }
        IPage<SysUserInfoVo> userInfoVoIPage = new Page<>();
        userInfoVoIPage.setRecords(sysUserInfoVos);
        userInfoVoIPage.setSize(userIPage.getSize());
        userInfoVoIPage.setPages(userIPage.getPages());
        userInfoVoIPage.setCurrent(userIPage.getCurrent());
        userInfoVoIPage.setTotal(userIPage.getTotal());
        return Result.OK(userInfoVoIPage);
    }

    public static double calculateTotalWorkHours(List<StaLocationClock> staLocationClockList) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 按时间排序打卡记录
        Collections.sort(staLocationClockList, new Comparator<StaLocationClock>() {
            public int compare(StaLocationClock o1, StaLocationClock o2) {
                return o1.getTime().compareTo(o2.getTime());
            }
        });
        long totalMillis = 0;
        Date signInTime = null;
        for (StaLocationClock clock : staLocationClockList) {
            Date clockTime = clock.getTime();
            if (clock.getTimeType() == 1) { // 签到
                signInTime = clockTime;
            } else if (clock.getTimeType() == 2) { // 签退
                if (signInTime != null) {
                    long diffInMillis = clockTime.getTime() - signInTime.getTime();
                    totalMillis += diffInMillis;
                    signInTime = null; // 重置签到时间
                } else {
                    // 没有对应的签到时间，可能是跨天签退，跳过处理或记录异常
                    // 这里可以记录异常或处理其他逻辑
                }
            }
        }
        // 将总毫秒数转换为小时（以 double 返回）
        double totalHours = totalMillis / (1000.0 * 60 * 60);
        return totalHours;
    }

    /**
     * @param realNameName
     * @param username
     * @param deptName
     * @param req
     * @return
     * @throws ParseException
     */
    @RequestMapping("exportXlsResignationDetails")
    public ModelAndView exportXlsResignationDetails(@RequestParam(name = "realNameName", required = false) String realNameName, @RequestParam(name = "username", required = false) String username, @RequestParam(name = "deptName", required = false) String deptName, HttpServletRequest req) throws ParseException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (WxlConvertUtils.isEmpty(sysUser)) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "登录失效");
            return mv;
        } //登录失效
        List<SysUser> sysUsers = sysUserService.listTaskTotal(realNameName, username, deptName);
        for (SysUser record : sysUsers) {
            if (WxlConvertUtils.isNotEmpty(record.getIdCard())) {
                String sex = this.judgeGender(record.getIdCard());
                int age = this.countAge(record.getIdCard());
                record.setSex(sex.equals("男") ? 0 : 1);
                record.setAge(age);
            }
        }
        if (sysUsers.isEmpty()) { //没有人员
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "人员为空");
            return mv;
        }
        //获取用户id
        List<String> userIds = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>().lambda().eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0).isNotNull(StaLocationClock::getTimeExpect).in(StaLocationClock::getSysUserId, userIds));
        //打卡记录分组
        Map<String, List<StaLocationClock>> groupedByUserId = staLocationClocks.stream().collect(Collectors.groupingBy(StaLocationClock::getSysUserId));
        //getTimeExpect 升序排序
        groupedByUserId.forEach((userId, staLocationClockList) -> {
            Collections.sort(staLocationClockList, Comparator.comparing(StaLocationClock::getTimeExpect).thenComparing(StaLocationClock::getStaOrderId));
        });
        Map<String, Double> userStatisticalWorkingHours = new HashMap<>();//时数
        Map<String, Integer> siLeng = new HashMap<>();//司龄
        for (Map.Entry<String, List<StaLocationClock>> entry : groupedByUserId.entrySet()) {
            List<StaLocationClock> staLocationClockList = entry.getValue();
            double v = this.calculateTotalWorkHours(staLocationClockList);
            Set<String> uniqueDates = new HashSet<>();
            for (StaLocationClock clock : staLocationClockList) {
                String dateStr = dateFormat.format(clock.getTime());
                uniqueDates.add(dateStr);
            }
            siLeng.put(entry.getKey(), uniqueDates.size());
            userStatisticalWorkingHours.put(entry.getKey(), v);
        }
        Set<String> storesIdList = new TreeSet<>();
        for (SysUser record : sysUsers) {
            if (WxlConvertUtils.isNotEmpty(record.getCurrentStoresId())) {
                storesIdList.add(record.getCurrentStoresId());
            }
            if (userStatisticalWorkingHours.get(record.getId()) != null) {
                String str = String.format("%.1f", userStatisticalWorkingHours.get(record.getId()));
                record.setStatisticalWorkingHours(WxlConvertUtils.isNotEmpty(Double.parseDouble(str)) ? Double.parseDouble(str) : 0.0);
                record.setCompanyServiceDays(siLeng.get(record.getId()));
            }
        }
        List<SysUserInfoVo> sysUserInfoVos = new ArrayList<>();
        for (SysUser record : sysUsers) {
            SysUserInfoVo sysUserInfoVo = new SysUserInfoVo();
            BeanUtils.copyProperties(record, sysUserInfoVo);
            sysUserInfoVo.setSex(record.getSex() == 0 ? "男" : "女");
            sysUserInfoVos.add(sysUserInfoVo);
        }
        List<SysDepart> storesList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, storesIdList)); //门店集合
        Set<String> deptRegionIdList = storesList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());
        List<SysDepart> deptRegionList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, deptRegionIdList));
        Set<String> deptBusinessDivisionIdList = deptRegionList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());
        List<SysDepart> deptBusinessDivisionList = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().in(SysDepart::getId, deptBusinessDivisionIdList));
        for (SysUserInfoVo sysUserInfoVo : sysUserInfoVos) {
            List<SysDepart> collect = storesList.stream().filter(s -> s.getId().equals(sysUserInfoVo.getCurrentStoresId())).collect(Collectors.toList());
            if (WxlConvertUtils.isNotEmpty(collect) && !collect.isEmpty()) {
                sysUserInfoVo.setStoreNo(collect.get(0).getStoreNo());
                String s = collect.get(0).getParentId();
                List<SysDepart> collect1 = deptRegionList.stream().filter(d -> d.getId().equals(s)).collect(Collectors.toList()); //区域
                sysUserInfoVo.setRegionName(collect1.get(0).getDepartName()); //区域
                String parentId = collect1.get(0).getParentId();
                List<SysDepart> collect2 = deptBusinessDivisionList.stream().filter(a -> a.getId().equals(parentId)).collect(Collectors.toList());
                sysUserInfoVo.setBusinessDivisionName(collect2.get(0).getDepartName());
            }
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "人员生命周期明细");
        mv.addObject(NormalExcelConstants.CLASS, SysUserInfoVo.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("人员生命周期明细", "导出人:" + sysUser.getRealname(), "人员生命周期明细"));
        mv.addObject(NormalExcelConstants.DATA_LIST, sysUserInfoVos);
        return mv;
    }

    @GetMapping("/pendingSettlementReview")
    public Result pendingSettlementReview(@RequestParam("oid") String oid) {
        return null;
    }

    /***
     * 任务单状态 maps
     * @return
     */
    public Map<Integer, String> taskOrderMaps() {
        return new HashMap<Integer, String>() {{
            put(0, "撤销");
            put(1, "完成");
            put(2, "申请中");
            put(3, "服务中");
            put(4, "待结算中");
            put(5, "待就职");
            put(6, "未通过审核");
            put(7, "通过审核后被剔除");
            put(8, "任务完成");
            put(9, "申请已过期");
            put(10, "完成审批中");
            put(11, "完成");
            put(13, "异动申请中");
            put(14, "异动");
        }};
    }

    /**
     * 填充 年龄 性别 生日
     *
     * @param pageList
     * @return
     */
    public IPage<StaOrder> populateAgeGenderBirth(IPage<StaOrder> pageList) {
        pageList.getRecords().forEach(r -> {
            if (r.getEnrollIdCard() != null && isIdCardNumberValid(r.getEnrollIdCard())) {
                if (isIdCardNumberValid(r.getEnrollIdCard())) {
                    String gender = judgeGender(r.getEnrollIdCard());
                    int age = countAge(r.getEnrollIdCard());
                    String birthFromIdCard = getBirthFromIdCard(r.getEnrollIdCard());
                    r.setAge(age);
                    r.setSex(gender);
                    r.setDateOfBirth(birthFromIdCard);
                }
            }
        });
        return pageList;
    }

    public List<StaOrder> populateAgeGender(List<StaOrder> orders) {
        orders.forEach(r -> {
            if (r.getEnrollIdCard().length() > 0 && isIdCardNumberValid(r.getEnrollIdCard())) {
                if (isIdCardNumberValid(r.getEnrollIdCard())) {
                    String gender = judgeGender(r.getEnrollIdCard());
                    int age = countAge(r.getEnrollIdCard());
                    String birthFromIdCard = getBirthFromIdCard(r.getEnrollIdCard());
                    r.setAge(age);
                    r.setSex(gender);
                    r.setDateOfBirth(birthFromIdCard);
                }
            } else {
                System.err.println(r.getId());
            }

        });
        return orders;
    }

    /**
     * @param queryWrapper
     * @param stateFlag       订单状态
     * @param selecteddeparts 门店店编
     * @param deptName        门店名称或店编
     * @param userName        用户姓名
     * @return
     */
    public QueryWrapper<StaOrder> orderConditionBuilder(QueryWrapper queryWrapper, String stateFlag, String selecteddeparts, String deptName, String userName) {
        //状态(工作中 完成 离职 异动)
        if (WxlConvertUtils.isEmpty(stateFlag)) {
            queryWrapper.notIn("state_flag", Arrays.asList(CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_2, CommonConstant.ORDER_STATUS_6, CommonConstant.ORDER_STATUS_7, CommonConstant.ORDER_STATUS_9));
        } else {
            queryWrapper.eq("state_flag", stateFlag);
        }
        //部门筛选
        if (WxlConvertUtils.isNotEmpty(selecteddeparts)) {
            SysDepart sysDepart = sysDepartService.getById(selecteddeparts);
            queryWrapper.likeRight("sys_org_code", sysDepart.getOrgCode());
        }
        //门店名称或店编
        if (WxlConvertUtils.isNotEmpty(deptName)) {
            SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, deptName));
            queryWrapper.likeRight("order_code", sysDepart.getOrgCode());
        }
        //人员姓名
        if (WxlConvertUtils.isNotEmpty(userName)) {
            queryWrapper.like("enroll_name", userName);
        }
        return queryWrapper;
    }
}

