package com.byun.modules.staffing.service;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.system.vo.LoginUser;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
/**
 * @Description: 任务单
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaOrderService extends IService<StaOrder> {

	/**
	 * @description: 根据电话给订单添加真实用户id
	 * <AUTHOR>
	 * @date 2021/12/4 18:00
	 * @version 1.0
	 */
	void addRealUserIdByPhone(String phone);

	/**
	 * @description: 用户撤销订单
	 * <AUTHOR>
	 * @date 2021/12/1 15:42
	 * @version 1.0
	 */
	void cancelOrder(StaOrder staOrder);

	/**
	 * @description: 批量点名
	 * <AUTHOR>
	 * @date 2021/11/23 15:05
	 * @version 1.0
	 */
	void adminBatchRollCall( List<StaOrder> staOrderList);
	/**
	 * @description: 批量审核拒绝
	 * <AUTHOR>
	 * @date 2021/11/23 10:02
	 * @version 1.0
	 */
	public void adminBatchReject( List<StaOrder> staOrderList);
	/**
	 * @description: 批量剔除已通过人员
	 * <AUTHOR>
	 * @date 2021/11/23 10:02
	 * @version 1.0
	 */
	public void adminBatchEliminate( List<StaOrder> staOrderList);
	/**
	 * @description: 批量审核通过
	 * <AUTHOR>
	 * @date 2021/11/23 10:02
	 * @version 1.0
	 */
	public void adminBatchAdopt( List<StaOrder> staOrderList) throws ParseException;
	/**
	 * @description: 创建订单
	 * <AUTHOR>
	 * @date 2021/11/22 8:53
	 * @version 1.0
	 */
	public Boolean createOrder(StaOrder staOrder,Integer adoptNum) ;

	/**
	 * 添加一对多
	 *
	 */
	public void saveMain(StaOrder staOrder,List<StaLocationClock> staLocationClockList) ;

	/**
	 * 修改一对多
	 *
	 */
	public void updateMain(StaOrder staOrder,List<StaLocationClock> staLocationClockList);

	/**
	 * 删除一对多
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	/***
	 *
	 * @param pageNo 页
	 * @param pageSize 条
	 * @param wid 任务ID
	 * @param realName 姓名
	 * @param userIdCard 身份证
	 * @param stateFlag 状态
	 * @return  List<StaLocationClock>
	 */
	Page<StaLocationClock> getPunchRecord(Integer pageNo, Integer pageSize, String wid,String businessDivisionName,String regionName ,String realName, String dateRangeBegin, String dateRangeEnd, String userIdCard, String stateFlag,String deptName,String deptNo,String order) throws ParseException;

	/**
	 * 优化版本：数据库级分页的打卡记录查询
	 *
	 * @param pageNo 页码
	 * @param pageSize 页大小
	 * @param wid 任务ID
	 * @param businessDivisionName 事业处名称
	 * @param regionName 区域名称
	 * @param realName 姓名
	 * @param dateRangeBegin 开始日期
	 * @param dateRangeEnd 结束日期
	 * @param userIdCard 身份证
	 * @param stateFlag 状态
	 * @param deptName 部门名称
	 * @param deptNo 部门编号
	 * @param order 排序
	 * @return 分页结果
	 * @throws ParseException 解析异常
	 */
	Page<StaLocationClock> getPunchRecordOptimized(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd, String userIdCard, String stateFlag, String deptName, String deptNo, String order) throws ParseException;

    Boolean agreeVariation(String oid);

	//Page<StaLocationClock> AllGetPunchRecord(Integer pageNo, Integer pageSize, String realName, String userIdCard, String dateRange, String stateFlag, String selecteddeparts,String deptName) throws ParseException;


    Boolean taskGradeScore(String sysUserId, String firmId, String staOrderId, String workId);
	//获取申请离职,异动的人数
	Map<String, Integer> fetchResignationAndMovementCount(LoginUser user);

	/**
	 *
	 * @param rosterMonth 排班月份
	 * @param companyName 门店
	 * @param enrollName 姓名
	 * @return
	 */
	List<JSONObject>  quickScheduling(String rosterMonth, String companyName, String enrollName,String storeNo);

	/**
	 * 考勤导出
	 * @param realName
	 * @param userIdCard
	 * @param dateRange
	 * @param stateFlag
	 * @param deptName
	 * @param selecteddeparts
	 * @return
	 * @throws ParseException
	 */
	List<StaLocationClock> exportXlsAttendanceExportAll(String businessDivisionName,String regionName ,String realName, String userIdCard, String dateRange, String stateFlag, String deptName, String selecteddeparts) throws ParseException;

	/**
	 * 管理员获取任务单
	 * @param pageNo
	 * @param pageSize
	 * @param queryWrapper
	 * @param request
	 * @return
	 */
    IPage<StaOrder> listByAdmin(Integer pageNo, Integer pageSize, QueryWrapper<StaOrder> queryWrapper, HttpServletRequest request);

    StaOrder agreeResign(String oid, String type, String taskScore);

	/**
	 * 获取用户月工时打卡时间为基准
	 * @param sysUserId
	 * @return
	 */
	Double getMonthlyWorkingHours(String sysUserId) throws ParseException;
}

