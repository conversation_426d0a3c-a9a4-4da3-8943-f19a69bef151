package com.byun.modules.staffing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaLocationClock;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * StaOrderService 优化测试
 * 测试数据库级分页优化的性能和正确性
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class StaOrderServiceOptimizationTest {

    @Autowired
    private IStaOrderService staOrderService;

    /**
     * 测试优化版本与原版本的性能对比
     */
    @Test
    public void testPaginationPerformanceComparison() {
        try {
            log.info("=== 开始性能对比测试 ===");
            
            // 测试参数
            Integer pageNo = 1;
            Integer pageSize = 10;
            String wid = null; // 可以设置具体的任务ID进行测试
            String businessDivisionName = null;
            String regionName = null;
            String realName = null;
            String dateRangeBegin = null;
            String dateRangeEnd = null;
            String userIdCard = null;
            String stateFlag = null;
            String deptName = null;
            String deptNo = null;
            String order = "desc";

            // 测试原版本
            long originalStartTime = System.currentTimeMillis();
            Page<StaLocationClock> originalResult = staOrderService.getPunchRecord(
                    pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                    dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, order);
            long originalEndTime = System.currentTimeMillis();
            long originalTime = originalEndTime - originalStartTime;

            // 测试优化版本
            long optimizedStartTime = System.currentTimeMillis();
            Page<StaLocationClock> optimizedResult = staOrderService.getPunchRecordOptimized(
                    pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                    dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, order);
            long optimizedEndTime = System.currentTimeMillis();
            long optimizedTime = optimizedEndTime - optimizedStartTime;

            // 性能对比分析
            log.info("=== 性能对比结果 ===");
            log.info("原版本耗时：{}ms，总记录数：{}，当前页记录数：{}", 
                    originalTime, originalResult.getTotal(), originalResult.getRecords().size());
            log.info("优化版本耗时：{}ms，总记录数：{}，当前页记录数：{}", 
                    optimizedTime, optimizedResult.getTotal(), optimizedResult.getRecords().size());

            if (optimizedTime < originalTime) {
                long improvement = originalTime - optimizedTime;
                double improvementPercent = (improvement * 100.0) / originalTime;
                log.info("🚀 性能提升：{}ms ({:.1f}%)", improvement, improvementPercent);
            } else {
                log.warn("⚠️ 优化版本耗时更长，可能需要进一步优化");
            }

            // 验证结果一致性（总记录数应该相同）
            if (originalResult.getTotal() == optimizedResult.getTotal()) {
                log.info("✅ 结果一致性验证通过：总记录数相同");
            } else {
                log.warn("⚠️ 结果一致性验证失败：总记录数不同");
            }

        } catch (Exception e) {
            log.error("❌ 性能对比测试失败", e);
        }
    }

    /**
     * 测试不同分页大小的性能
     */
    @Test
    public void testDifferentPageSizes() {
        try {
            log.info("=== 开始不同分页大小测试 ===");
            
            int[] pageSizes = {10, 20, 50, 100};
            
            for (int pageSize : pageSizes) {
                log.info("--- 测试分页大小：{} ---", pageSize);
                
                long startTime = System.currentTimeMillis();
                Page<StaLocationClock> result = staOrderService.getPunchRecordOptimized(
                        1, pageSize, null, null, null, null,
                        null, null, null, null, null, null, "desc");
                long endTime = System.currentTimeMillis();
                
                log.info("分页大小：{}，耗时：{}ms，总记录数：{}，当前页记录数：{}", 
                        pageSize, (endTime - startTime), result.getTotal(), result.getRecords().size());
            }
            
        } catch (Exception e) {
            log.error("❌ 不同分页大小测试失败", e);
        }
    }

    /**
     * 测试边界条件
     */
    @Test
    public void testBoundaryConditions() {
        try {
            log.info("=== 开始边界条件测试 ===");
            
            // 测试第一页
            Page<StaLocationClock> firstPage = staOrderService.getPunchRecordOptimized(
                    1, 10, null, null, null, null,
                    null, null, null, null, null, null, "desc");
            log.info("第一页：总记录数：{}，当前页记录数：{}", firstPage.getTotal(), firstPage.getRecords().size());
            
            // 测试最后一页
            if (firstPage.getTotal() > 0) {
                long totalPages = (firstPage.getTotal() + 9) / 10; // 向上取整
                Page<StaLocationClock> lastPage = staOrderService.getPunchRecordOptimized(
                        (int) totalPages, 10, null, null, null, null,
                        null, null, null, null, null, null, "desc");
                log.info("最后一页：总记录数：{}，当前页记录数：{}", lastPage.getTotal(), lastPage.getRecords().size());
            }
            
            // 测试超出范围的页码
            Page<StaLocationClock> outOfRangePage = staOrderService.getPunchRecordOptimized(
                    999999, 10, null, null, null, null,
                    null, null, null, null, null, null, "desc");
            log.info("超出范围页码：总记录数：{}，当前页记录数：{}", outOfRangePage.getTotal(), outOfRangePage.getRecords().size());
            
        } catch (Exception e) {
            log.error("❌ 边界条件测试失败", e);
        }
    }
}
